[2025-08-23 23:41:57,456][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-08-23 23:41:57,456][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-08-23 23:41:59,689][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-08-23 23:41:59,692][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-08-23 23:42:02,610][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-08-23 23:42:02,610][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-08-23 23:42:02,623][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-08-23 23:42:02,624][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-08-23 23:42:07,677][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-08-23 23:42:07,678][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-08-23 23:42:07,678][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-08-23 23:42:07,678][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-08-23 23:42:07,679][root][INFO] - Adding 67158 samples from SceneFlow
[2025-08-23 23:42:07,679][root][INFO] - Adding 67158 samples from SceneFlow
[2025-08-23 23:42:07,680][root][INFO] - Adding 67158 samples from SceneFlow
[2025-08-23 23:42:07,681][root][INFO] - Adding 67158 samples from SceneFlow
[2025-08-23 23:42:08,047][dinov3][INFO] - using base=100 for rope new
[2025-08-23 23:42:08,047][dinov3][INFO] - using min_period=None for rope new
[2025-08-23 23:42:08,048][dinov3][INFO] - using max_period=None for rope new
[2025-08-23 23:42:08,049][dinov3][INFO] - using normalize_coords=separate for rope new
[2025-08-23 23:42:08,050][dinov3][INFO] - using shift_coords=None for rope new
[2025-08-23 23:42:08,050][dinov3][INFO] - using rescale_coords=2 for rope new
[2025-08-23 23:42:08,051][dinov3][INFO] - using jitter_coords=None for rope new
[2025-08-23 23:42:08,051][dinov3][INFO] - using dtype=fp32 for rope new
[2025-08-23 23:42:08,052][dinov3][INFO] - using mlp layer as FFN
[2025-08-23 23:42:08,078][dinov3][INFO] - using base=100 for rope new
[2025-08-23 23:42:08,079][dinov3][INFO] - using min_period=None for rope new
[2025-08-23 23:42:08,079][dinov3][INFO] - using max_period=None for rope new
[2025-08-23 23:42:08,080][dinov3][INFO] - using normalize_coords=separate for rope new
[2025-08-23 23:42:08,080][dinov3][INFO] - using shift_coords=None for rope new
[2025-08-23 23:42:08,080][dinov3][INFO] - using rescale_coords=2 for rope new
[2025-08-23 23:42:08,081][dinov3][INFO] - using jitter_coords=None for rope new
[2025-08-23 23:42:08,081][dinov3][INFO] - using dtype=fp32 for rope new
[2025-08-23 23:42:08,082][dinov3][INFO] - using mlp layer as FFN
[2025-08-23 23:42:08,124][dinov3][INFO] - using base=100 for rope new
[2025-08-23 23:42:08,124][dinov3][INFO] - using min_period=None for rope new
[2025-08-23 23:42:08,125][dinov3][INFO] - using max_period=None for rope new
[2025-08-23 23:42:08,125][dinov3][INFO] - using normalize_coords=separate for rope new
[2025-08-23 23:42:08,126][dinov3][INFO] - using shift_coords=None for rope new
[2025-08-23 23:42:08,126][dinov3][INFO] - using rescale_coords=2 for rope new
[2025-08-23 23:42:08,127][dinov3][INFO] - using jitter_coords=None for rope new
[2025-08-23 23:42:08,127][dinov3][INFO] - using dtype=fp32 for rope new
[2025-08-23 23:42:08,128][dinov3][INFO] - using mlp layer as FFN
[2025-08-23 23:42:08,146][dinov3][INFO] - using base=100 for rope new
[2025-08-23 23:42:08,147][dinov3][INFO] - using min_period=None for rope new
[2025-08-23 23:42:08,147][dinov3][INFO] - using max_period=None for rope new
[2025-08-23 23:42:08,148][dinov3][INFO] - using normalize_coords=separate for rope new
[2025-08-23 23:42:08,148][dinov3][INFO] - using shift_coords=None for rope new
[2025-08-23 23:42:08,148][dinov3][INFO] - using rescale_coords=2 for rope new
[2025-08-23 23:42:08,149][dinov3][INFO] - using jitter_coords=None for rope new
[2025-08-23 23:42:08,149][dinov3][INFO] - using dtype=fp32 for rope new
[2025-08-23 23:42:08,150][dinov3][INFO] - using mlp layer as FFN

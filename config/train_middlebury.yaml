wandb: {}
project_name: middlebury
restore_ckpt: "./sceneflow.pth"
# restore_ckpt: "/data2/cjd/mono_fusion/checkpoints/sceneflow.pth"
logdir: './checkpoints/middlebury/'
encoder: 'vitl'
batch_size: 8
train_datasets: ['middlebury_finetune']
lr: 1e-4
wdecay: 1e-5
total_step: 100000
save_frequency: 20
save_path: './checkpoints/middlebury/'
val_frequency: 20
image_size: [352, 896]
train_iters: 20
valid_iters: 32
val_dataset: 'kitti'
corr_implementation: "reg"
corr_levels: 2
corr_radius: 4
n_downsample: 2
n_gru_layers: 3
hidden_dims: [128, 128, 128]
max_disp: 768
saturation_range: [0.0, 1.4]
do_flip: False
spatial_scale: [-0.2, 0.4]
noyjitter: True
num_gpu: 4
seed: 666

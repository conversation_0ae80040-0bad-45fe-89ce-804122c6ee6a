wandb: {}
project_name: 
restore_ckpt: "./sceneflow.pth"
# restore_ckpt: "/data2/cjd/mono_fusion/checkpoints/sceneflow.pth"
logdir: './checkpoints/eth3d/'
encoder: 'vitl'
batch_size: 12
train_datasets: ['eth3d_finetune']
lr: 1e-4
wdecay: 1e-5
total_step: 100000
save_frequency: 2500
save_path: './checkpoints/eth3d/'
val_frequency: 2500
image_size: [320, 736]
train_iters: 21
valid_iters: 32
val_dataset: 'kitti'
corr_implementation: "reg"
corr_levels: 2
corr_radius: 4
n_downsample: 2
n_gru_layers: 3
hidden_dims: [128, 128, 128]
max_disp: 192
saturation_range: [0.0, 1.4]
do_flip: False
spatial_scale: [-0.2, 0.4]
noyjitter: True
num_gpu: 4
seed: 666

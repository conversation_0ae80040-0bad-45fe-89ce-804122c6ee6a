#!/usr/bin/env python3

def verify_pyramid_sizes(input_h, input_w):
    """Verify what the correct pyramid sizes should be."""
    print(f"Input size: {input_h} × {input_w}")
    print("Expected pyramid sizes:")
    print(f"  1/4 scale: {input_h//4} × {input_w//4}")
    print(f"  1/8 scale: {input_h//8} × {input_w//8}")
    print(f"  1/16 scale: {input_h//16} × {input_w//16}")
    print(f"  1/32 scale: {input_h//32} × {input_w//32}")
    
    print("\nWith patch_size=16:")
    patch_h = input_h // 16
    patch_w = input_w // 16
    print(f"  patch_h = {patch_h}, patch_w = {patch_w}")
    print(f"  1/4 scale: patch×4 = {patch_h*4} × {patch_w*4}")
    print(f"  1/8 scale: patch×2 = {patch_h*2} × {patch_w*2}")
    print(f"  1/16 scale: patch×1 = {patch_h} × {patch_w}")
    print(f"  1/32 scale: patch÷2 = {patch_h//2} × {patch_w//2}")

if __name__ == "__main__":
    verify_pyramid_sizes(1024, 2048)

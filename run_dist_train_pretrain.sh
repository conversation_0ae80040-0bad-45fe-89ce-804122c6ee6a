#!/usr/bin/env bash
# 4-GPU distributed training launcher for train_pretrain.py
# Usage:
#   bash run_dist_train_pretrain.sh                     # default config
#   bash run_dist_train_pretrain.sh train_iters=6 lr=1e-4 total_step=60000  # with overrides
# Environment variables you can override before running:
#   NUM_GPUS (default 4)
#   CUDA_VISIBLE_DEVICES (default 0,1,2,3)
#   MASTER_PORT (default 29501)
#   OUTPUT_ROOT (default outputs)
#   RUN_NAME (default auto timestamp)

set -euo pipefail

NUM_GPUS=${NUM_GPUS:-4}
export CUDA_VISIBLE_DEVICES=${CUDA_VISIBLE_DEVICES:-0,1,2,3}
export MASTER_PORT=${MASTER_PORT:-29501}
OUTPUT_ROOT=${OUTPUT_ROOT:-outputs}
RUN_NAME=${RUN_NAME:-pretrain_$(date +%Y%m%d_%H%M%S)}
SAVE_DIR="${OUTPUT_ROOT}/${RUN_NAME}"
mkdir -p "${SAVE_DIR}"

# Pass Hydra override to set save_path (assuming your config has save_path key)
EXTRA_ARGS=("save_path=${SAVE_DIR}" "$@")

echo "Launching distributed training:" >&2
echo " GPUs:        ${NUM_GPUS}" >&2
echo " Devices:     ${CUDA_VISIBLE_DEVICES}" >&2
echo " Master Port: ${MASTER_PORT}" >&2
echo " Save Dir:    ${SAVE_DIR}" >&2
echo " Extra Args:  ${EXTRA_ARGS[*]}" >&2

torchrun --standalone --nnodes=1 --nproc_per_node=${NUM_GPUS} train_pretrain.py "${EXTRA_ARGS[@]}"

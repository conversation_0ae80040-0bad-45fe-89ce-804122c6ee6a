{"os": "Linux-5.15.0-86-generic-x86_64-with-glibc2.35", "python": "CPython 3.12.3", "startedAt": "2025-06-17T09:38:36.271186Z", "program": "/storage/pmj/zhi/project/MonSter_origin/train_sceneflow.py", "codePath": "train_sceneflow.py", "root": "/storage/pmj/zhi/project/MonSter_origin", "host": "c263-gz3-server-iv-001", "executable": "/home/<USER>/mambaforge/envs/sm/bin/python3.12", "codePathLocal": "train_sceneflow.py", "cpu_count": 16, "cpu_count_logical": 32, "gpu": "NVIDIA A100-PCIE-40GB", "gpu_count": 4, "disk": {"/": {"total": "507100475392", "used": "216298962944"}}, "memory": {"total": "270454984704"}, "cpu": {"count": 16, "countLogical": 32}, "gpu_nvidia": [{"name": "NVIDIA A100-PCIE-40GB", "memoryTotal": "42949672960", "cudaCores": 6912, "architecture": "Ampere"}, {"name": "NVIDIA A100-PCIE-40GB", "memoryTotal": "42949672960", "cudaCores": 6912, "architecture": "Ampere"}, {"name": "NVIDIA A100-PCIE-40GB", "memoryTotal": "42949672960", "cudaCores": 6912, "architecture": "Ampere"}, {"name": "NVIDIA A100-PCIE-40GB", "memoryTotal": "42949672960", "cudaCores": 6912, "architecture": "Ampere"}], "cudaVersion": "12.8"}
2025-06-17 17:38:36,045 INFO    MainThread:4083745 [wandb_setup.py:_flush():67] Current SDK version is 0.19.9
2025-06-17 17:38:36,049 INFO    MainThread:4083745 [wandb_setup.py:_flush():67] Configure stats pid to 4083745
2025-06-17 17:38:36,050 INFO    MainThread:4083745 [wandb_setup.py:_flush():67] Loading settings from /home/<USER>/.config/wandb/settings
2025-06-17 17:38:36,050 INFO    MainThread:4083745 [wandb_setup.py:_flush():67] Loading settings from /storage/pmj/zhi/project/MonSter_origin/wandb/settings
2025-06-17 17:38:36,050 INFO    MainThread:4083745 [wandb_setup.py:_flush():67] Loading settings from environment variables
2025-06-17 17:38:36,051 INFO    MainThread:4083745 [wandb_init.py:setup_run_log_directory():662] Logging user logs to /storage/pmj/zhi/project/MonSter_origin/wandb/offline-run-20250617_173836-ys9d807u/logs/debug.log
2025-06-17 17:38:36,051 INFO    MainThread:4083745 [wandb_init.py:setup_run_log_directory():663] Logging internal logs to /storage/pmj/zhi/project/MonSter_origin/wandb/offline-run-20250617_173836-ys9d807u/logs/debug-internal.log
2025-06-17 17:38:36,051 INFO    MainThread:4083745 [wandb_init.py:init():781] calling init triggers
2025-06-17 17:38:36,052 INFO    MainThread:4083745 [wandb_init.py:init():786] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-06-17 17:38:36,052 INFO    MainThread:4083745 [wandb_init.py:init():809] starting backend
2025-06-17 17:38:36,262 INFO    MainThread:4083745 [wandb_init.py:init():813] sending inform_init request
2025-06-17 17:38:36,269 INFO    MainThread:4083745 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-06-17 17:38:36,270 INFO    MainThread:4083745 [wandb_init.py:init():823] backend started and connected
2025-06-17 17:38:36,274 INFO    MainThread:4083745 [wandb_init.py:init():915] updated telemetry
2025-06-17 17:38:36,275 INFO    MainThread:4083745 [wandb_init.py:init():939] communicating run to backend with 90.0 second timeout
2025-06-17 17:38:36,394 INFO    MainThread:4083745 [wandb_init.py:init():1014] starting run threads in backend
2025-06-17 17:38:36,550 INFO    MainThread:4083745 [wandb_run.py:_console_start():2454] atexit reg
2025-06-17 17:38:36,551 INFO    MainThread:4083745 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-06-17 17:38:36,551 INFO    MainThread:4083745 [wandb_run.py:_redirect():2371] Wrapping output streams.
2025-06-17 17:38:36,552 INFO    MainThread:4083745 [wandb_run.py:_redirect():2394] Redirects installed.
2025-06-17 17:38:36,554 INFO    MainThread:4083745 [wandb_init.py:init():1056] run started, returning control to user process
2025-06-17 17:38:36,555 INFO    MainThread:4083745 [wandb_run.py:_config_callback():1327] config_cb None None {'wandb': {}, 'project_name': 'sceneflow', 'restore_ckpt': 'checkpoint/sceneflow.pth', 'logdir': './checkpoints/sceneflow/', 'encoder': 'vitl', 'batch_size': 12, 'train_datasets': ['sceneflow'], 'lr': 0.0002, 'wdecay': 1e-05, 'total_step': 200000, 'save_frequency': 5000, 'save_path': './checkpoints/sceneflow/', 'val_frequency': 5000, 'image_size': [320, 736], 'train_iters': 22, 'valid_iters': 32, 'val_dataset': 'kitti', 'corr_implementation': 'reg', 'corr_levels': 2, 'corr_radius': 4, 'n_downsample': 2, 'n_gru_layers': 3, 'hidden_dims': [128, 128, 128], 'max_disp': 192, 'saturation_range': [0.7, 1.3], 'do_flip': False, 'spatial_scale': [-0.2, 0.5], 'noyjitter': True, 'num_gpu': 4, 'seed': 666}

2025-06-17 17:30:06,375 INFO    MainThread:4076037 [wandb_setup.py:_flush():67] Current SDK version is 0.19.9
2025-06-17 17:30:06,378 INFO    MainThread:4076037 [wandb_setup.py:_flush():67] Configure stats pid to 4076037
2025-06-17 17:30:06,379 INFO    MainThread:4076037 [wandb_setup.py:_flush():67] Loading settings from /home/<USER>/.config/wandb/settings
2025-06-17 17:30:06,380 INFO    MainThread:4076037 [wandb_setup.py:_flush():67] Loading settings from /storage/pmj/zhi/project/MonSter_origin/wandb/settings
2025-06-17 17:30:06,380 INFO    MainThread:4076037 [wandb_setup.py:_flush():67] Loading settings from environment variables
2025-06-17 17:30:06,380 INFO    MainThread:4076037 [wandb_init.py:setup_run_log_directory():662] Logging user logs to /storage/pmj/zhi/project/MonSter_origin/wandb/run-20250617_173006-2z3hucoz/logs/debug.log
2025-06-17 17:30:06,381 INFO    MainThread:4076037 [wandb_init.py:setup_run_log_directory():663] Logging internal logs to /storage/pmj/zhi/project/MonSter_origin/wandb/run-20250617_173006-2z3hucoz/logs/debug-internal.log
2025-06-17 17:30:06,381 INFO    MainThread:4076037 [wandb_init.py:init():781] calling init triggers
2025-06-17 17:30:06,381 INFO    MainThread:4076037 [wandb_init.py:init():786] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-06-17 17:30:06,382 INFO    MainThread:4076037 [wandb_init.py:init():809] starting backend
2025-06-17 17:30:06,382 INFO    MainThread:4076037 [wandb_init.py:init():813] sending inform_init request
2025-06-17 17:30:06,390 INFO    MainThread:4076037 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-06-17 17:30:06,390 INFO    MainThread:4076037 [wandb_init.py:init():823] backend started and connected
2025-06-17 17:30:06,394 INFO    MainThread:4076037 [wandb_init.py:init():915] updated telemetry
2025-06-17 17:30:06,399 INFO    MainThread:4076037 [wandb_init.py:init():939] communicating run to backend with 90.0 second timeout
2025-06-17 17:30:37,517 INFO    Thread-2 (wrapped_target):4076037 [retry.py:__call__():175] [no run ID] Retry attempt failed:
Traceback (most recent call last):
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/urllib3/connection.py", line 174, in _new_conn
    conn = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/urllib3/util/connection.py", line 95, in create_connection
    raise err
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/urllib3/util/connection.py", line 85, in create_connection
    sock.connect(sa)
TimeoutError: timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/urllib3/connectionpool.py", line 716, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/urllib3/connectionpool.py", line 404, in _make_request
    self._validate_conn(conn)
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/urllib3/connectionpool.py", line 1061, in _validate_conn
    conn.connect()
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/urllib3/connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/urllib3/connection.py", line 179, in _new_conn
    raise ConnectTimeoutError(
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x7fe0b8f8f620>, 'Connection to api.wandb.ai timed out. (connect timeout=20)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/requests/adapters.py", line 489, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/urllib3/connectionpool.py", line 802, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7fe0b8f8f620>, 'Connection to api.wandb.ai timed out. (connect timeout=20)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/wandb/sdk/lib/retry.py", line 134, in __call__
    result = self._call_fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/wandb/sdk/internal/internal_api.py", line 402, in execute
    return self.client.execute(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/wandb/vendor/gql-0.2.0/wandb_gql/client.py", line 52, in execute
    result = self._get_result(document, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/wandb/vendor/gql-0.2.0/wandb_gql/client.py", line 60, in _get_result
    return self.transport.execute(document, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/wandb/sdk/lib/gql_request.py", line 58, in execute
    request = self.session.post(self.url, **post_args)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/requests/adapters.py", line 553, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7fe0b8f8f620>, 'Connection to api.wandb.ai timed out. (connect timeout=20)'))

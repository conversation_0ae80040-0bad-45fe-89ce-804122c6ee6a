from __future__ import print_function, division
import sys
sys.path.append('core')

import argparse
import time
import logging
import numpy as np
import torch
from tqdm import tqdm
from core.selective_igev import IGEVStereo
import core.stereo_datasets as datasets
from core.utils.utils import InputPadder
from PIL import Image
import cv2
import torch.nn as nn
from datetime import datetime
from torch.cuda.amp import autocast

import os
os.environ['CUDA_VISIBLE_DEVICES'] = '3'

def count_parameters(model):
    return sum(p.numel() for p in model.parameters() if p.requires_grad)




@torch.no_grad()
def validate_eth3d(model, iters=32, mixed_prec=False, restore_ckpt=None):
    """ Peform validation using the ETH3D (train) split """
    model.eval()
    aug_params = {}
    val_dataset = datasets.ETH3D(aug_params)

    out_list, epe_list = [], []
    for val_id in tqdm(range(len(val_dataset)), desc="ETH3D Validation"):
        (imageL_file, imageR_file, GT_file), image1, image2, flow_gt, valid_gt = val_dataset[val_id]
        image1 = image1[None].cuda()
        image2 = image2[None].cuda()

        padder = InputPadder(image1.shape, divis_by=32)
        image1, image2 = padder.pad(image1, image2)

        with autocast(enabled=mixed_prec):
            flow_pr, _, _ = model(image1, image2, iters=iters, test_mode=True)
        flow_pr = padder.unpad(flow_pr.float()).cpu().squeeze(0)
        assert flow_pr.shape == flow_gt.shape, (flow_pr.shape, flow_gt.shape)
        epe = torch.sum((flow_pr - flow_gt)**2, dim=0).sqrt()

        epe_flattened = epe.flatten()

        occ_mask = Image.open(GT_file.replace('disp0GT.pfm', 'mask0nocc.png'))
        occ_mask = np.ascontiguousarray(occ_mask).flatten()
        val = (valid_gt.flatten() >= 0.5) & (occ_mask == 255)

        out = (epe_flattened > 1.0)
        image_out = out[val].float().mean().item()
        image_epe = epe_flattened[val].mean().item()
        logging.info(f"ETH3D {val_id+1} out of {len(val_dataset)}. EPE {round(image_epe,4)} D1 {round(image_out,4)}")
        epe_list.append(image_epe)
        out_list.append(image_out)

    epe_list = np.array(epe_list)
    out_list = np.array(out_list)

    epe = np.mean(epe_list)
    d1 = 100 * np.mean(out_list)

    print("Validation ETH3D: EPE %f, D1 %f" % (epe, d1))
    
    # Write results to test.txt file
    with open('test.txt', 'a') as f:
        f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ETH3D Validation Results:\n")
        if restore_ckpt:
            f.write(f"Model weights: {restore_ckpt}\n")
        f.write(f"Dataset: ETH3D training set\n")
        f.write(f"Iterations: {iters}, Mixed precision: {mixed_prec}\n")
        f.write(f"EPE: {epe:.4f}, D1: {d1:.2f}%\n")
        f.write("-" * 60 + "\n")
    
    return {'eth3d-epe': epe, 'eth3d-d1': d1}


def load_kitti_disp_gt(disp_occ_path, year):
    """Load both occluded and non-occluded ground truth disparity maps"""
    import cv2
    import os
    
    # Load occluded (all area) disparity
    disp_occ = cv2.imread(disp_occ_path, cv2.IMREAD_ANYDEPTH) / 256.0
    disp_occ = torch.from_numpy(disp_occ).float()
    
    # Construct non-occluded disparity path
    if year == 2012:
        # disp_occ -> disp_noc (e.g., /disp_occ/xxx_10.png -> /disp_noc/xxx_10.png)
        disp_noc_path = disp_occ_path.replace('/disp_occ/', '/disp_noc/')
    else:  # year == 2015
        # disp_occ_0 -> disp_noc_0 (e.g., /disp_occ_0/xxx_10.png -> /disp_noc_0/xxx_10.png)
        disp_noc_path = disp_occ_path.replace('/disp_occ_0/', '/disp_noc_0/')
    
    # Load non-occluded disparity if exists
    if os.path.exists(disp_noc_path):
        disp_noc = cv2.imread(disp_noc_path, cv2.IMREAD_ANYDEPTH) / 256.0
        disp_noc = torch.from_numpy(disp_noc).float()
    else:
        # If non-occluded file doesn't exist, use occluded as fallback
        disp_noc = disp_occ.clone()
    
    return disp_occ, disp_noc

@torch.no_grad()
def validate_kitti(model, iters=32, mixed_prec=False, year=2012, restore_ckpt=None):
    """ Peform validation using the KITTI (train) split with both occluded and non-occluded evaluation """
    model.eval()
    aug_params = {}
    val_dataset = datasets.KITTI(aug_params, image_set='training', year=year)
    torch.backends.cudnn.benchmark = True

    # Separate lists for occluded (all area) and non-occluded area evaluation
    out_list_occ, epe_list_occ = [], []
    out_list_noc, epe_list_noc = [], []
    elapsed_list = []
    
    for val_id in tqdm(range(len(val_dataset)), desc=f"KITTI {year} Validation"):
        file_paths, image1, image2, flow_gt_occ, valid_gt = val_dataset[val_id]
        
        # Get the disparity file path from the file_paths list
        # file_paths = [img1_path, img2_path, disp_path]
        disp_occ_path = file_paths[2]
        
        # Load both occluded and non-occluded ground truth
        flow_gt_occ_full, flow_gt_noc = load_kitti_disp_gt(disp_occ_path, year)
        
        # Convert to tensor format (add channel dimension)
        flow_gt_occ_full = flow_gt_occ_full.unsqueeze(0)  # [1, H, W]
        flow_gt_noc = flow_gt_noc.unsqueeze(0)  # [1, H, W]
        
        image1 = image1[None].cuda()
        image2 = image2[None].cuda()

        padder = InputPadder(image1.shape, divis_by=32)
        image1, image2 = padder.pad(image1, image2)

        with autocast(enabled=mixed_prec):
            start = time.time()
            flow_pr, _, _ = model(image1, image2, iters=iters, test_mode=True)
            end = time.time()

        if val_id > 50:
            elapsed_list.append(end - start)

        flow_pr = padder.unpad(flow_pr).cpu().squeeze(0)

        # Evaluation for occluded area (all area)
        assert flow_pr.shape == flow_gt_occ_full.shape, (flow_pr.shape, flow_gt_occ_full.shape)
        epe_occ = torch.sum((flow_pr - flow_gt_occ_full)**2, dim=0).sqrt()
        
        epe_occ_flattened = epe_occ.flatten()
        # Create valid mask for occluded evaluation
        valid_occ = (flow_gt_occ_full.abs().flatten() > 0) & (flow_gt_occ_full.abs().flatten() < 192)
        
        out_occ = (epe_occ_flattened > 3.0)
        
        epe_list_occ.append(epe_occ_flattened[valid_occ].mean().item())
        out_list_occ.append(out_occ[valid_occ].cpu().numpy())

        # Evaluation for non-occluded area
        assert flow_pr.shape == flow_gt_noc.shape, (flow_pr.shape, flow_gt_noc.shape)
        epe_noc = torch.sum((flow_pr - flow_gt_noc)**2, dim=0).sqrt()
        
        epe_noc_flattened = epe_noc.flatten()
        # Create valid mask for non-occluded evaluation (only where non-occluded GT exists)
        valid_noc = (flow_gt_noc.abs().flatten() > 0) & (flow_gt_noc.abs().flatten() < 192)
        
        out_noc = (epe_noc_flattened > 3.0)
        
        epe_list_noc.append(epe_noc_flattened[valid_noc].mean().item())
        out_list_noc.append(out_noc[valid_noc].cpu().numpy())

    # Compute metrics for occluded (all area)
    epe_list_occ = np.array(epe_list_occ)
    out_list_occ = np.concatenate(out_list_occ)
    epe_occ = np.mean(epe_list_occ)
    d1_occ = 100 * np.mean(out_list_occ)

    # Compute metrics for non-occluded area
    epe_list_noc = np.array(epe_list_noc)
    out_list_noc = np.concatenate(out_list_noc)
    epe_noc = np.mean(epe_list_noc)
    d1_noc = 100 * np.mean(out_list_noc)

    avg_runtime = np.mean(elapsed_list)

    print(f"Validation KITTI {year}:")
    print(f"  All area (occ):     EPE {epe_occ:.3f}, D1 {d1_occ:.2f}%")
    print(f"  Non-occluded area:  EPE {epe_noc:.3f}, D1 {d1_noc:.2f}%")
    print(f"  Runtime: {format(avg_runtime, '.3f')}s")
    
    # Write results to test.txt file
    with open('test.txt', 'a') as f:
        f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] KITTI {year} Validation Results:\n")
        if restore_ckpt:
            f.write(f"Model weights: {restore_ckpt}\n")
        f.write(f"Dataset: KITTI {year} training set\n")
        f.write(f"Iterations: {iters}, Mixed precision: {mixed_prec}\n")
        f.write(f"All area (occ):     EPE {epe_occ:.4f}, D1 {d1_occ:.2f}%\n")
        f.write(f"Non-occluded area:  EPE {epe_noc:.4f}, D1 {d1_noc:.2f}%\n")
        f.write(f"Runtime: {format(avg_runtime, '.3f')}s\n")
        f.write("-" * 60 + "\n")
    
    return {
        f'kitti{year}-epe-occ': epe_occ, 
        f'kitti{year}-d1-occ': d1_occ,
        f'kitti{year}-epe-noc': epe_noc, 
        f'kitti{year}-d1-noc': d1_noc
    }


@torch.no_grad()
def validate_sceneflow_origin(model, iters=32, mixed_prec=False, restore_ckpt=None):
    """ Peform validation using the Scene Flow (TEST) split """
    model.eval()
    val_dataset = datasets.SceneFlowDatasets(dstype='frames_finalpass', things_test=True)
    val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=8, pin_memory=True, shuffle=False, num_workers=8, drop_last=False)

    out_list, epe_list, rmae_list = [], [], []
    for i_batch, (_, *data_blob) in enumerate(tqdm(val_loader)):
        image1, image2, flow_gt, occ_mask, valid_gt = [x.cuda() for x in data_blob]

        padder = InputPadder(image1.shape, divis_by=32)
        image1, image2 = padder.pad(image1, image2)

        with autocast(enabled=mixed_prec):
            flow_pr, _, _ = model(image1, image2, iters=iters, test_mode=True)
        flow_pr = padder.unpad(flow_pr)
        assert flow_pr.shape == flow_gt.shape, (flow_pr.shape, flow_gt.shape)

        epe = torch.abs(flow_pr - flow_gt)

        epe = epe.flatten()
        val = (valid_gt.flatten() >= 0.5) & (flow_gt.abs().flatten() < 192)

        out = (epe > 1.0).float()
        epe_list.append(epe[val].mean().item())
        out_list.append(out[val].mean().item())

        if i_batch % 10 ==0:
            print("Iter {:02d} EPE: {:.3f} D1: {:.3f}".format(i_batch, np.mean(np.array(epe_list)), 100*np.mean(np.array(out_list))))


    epe_list = np.array(epe_list)
    out_list = np.array(out_list)

    epe = np.mean(epe_list)
    d1 = 100 * np.mean(out_list)

    print("Validation Scene Flow: %f, %f" % (epe, d1))
    
    # Write results to test.txt file
    with open('test.txt', 'a') as f:
        f.write(f"Scene Flow (Original) Validation Results:\n")
        if restore_ckpt:
            f.write(f"Model weights: {restore_ckpt}\n")
        f.write(f"Dataset: Scene Flow test set (frames_finalpass)\n")
        f.write(f"Iterations: {iters}, Mixed precision: {mixed_prec}\n")
        f.write(f"EPE: {epe:.4f}, D1: {d1:.2f}%\n")
        f.write("-" * 60 + "\n")
    
    return {'scene-flow-epe': epe, 'scene-flow-d1': d1}



@torch.no_grad()
def validate_sceneflow_edge(model, iters=32, mixed_prec=False, restore_ckpt=None):
    """ Peform validation using the Scene Flow (TEST) split """

    model.eval()
    val_dataset = datasets.SceneFlowDatasets(dstype='frames_finalpass', things_test=True)
    val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=8, pin_memory=True, shuffle=False, num_workers=4, drop_last=False)

    # 三个区域的指标列表：Overall, Edge, Non-Edge
    out_list_overall, epe_list_overall = [], []
    out_list_edge, epe_list_edge = [], []
    out_list_nonedge, epe_list_nonedge = [], []
    
    for i_batch, (_, *data_blob) in enumerate(tqdm(val_loader)):
        image1, image2, flow_gt, occ_mask, valid_gt = [x.cuda() for x in data_blob]

        padder = InputPadder(image1.shape, divis_by=32)
        image1, image2 = padder.pad(image1, image2)

        with autocast(enabled=mixed_prec):
            flow_pr, _, _ = model(image1, image2, iters=iters, test_mode=True)
        flow_pr = padder.unpad(flow_pr)
        assert flow_pr.shape == flow_gt.shape, (flow_pr.shape, flow_gt.shape)

        epe = torch.abs(flow_pr - flow_gt)
        
        # 计算Overall区域的指标（与validate_sceneflow_origin保持一致）
        epe_flat = epe.flatten()
        val_overall = (valid_gt.flatten() >= 0.5) & (flow_gt.abs().flatten() < 192)
        out_overall = (epe_flat > 1.0).float()
        epe_list_overall.append(epe_flat[val_overall].mean().item())
        out_list_overall.append(out_overall[val_overall].mean().item())
        
        # 对batch中的每个样本进行处理（用于计算边缘区域）
        batch_size = flow_gt.shape[0]
        
        for b in range(batch_size):
            # 获取单个样本的数据
            epe_single = epe[b].flatten()
            flow_gt_single = flow_gt[b]
            valid_gt_single = valid_gt[b]
            
            # 基本的有效性mask
            val_basic = (valid_gt_single.flatten() >= 0.5) & (flow_gt_single.abs().flatten() < 192)
            
            # 使用Canny边缘检测在Ground Truth视差图上检测边缘
            # 将视差图转换为numpy数组并归一化到0-255范围用于Canny检测
            flow_gt_np = flow_gt_single.cpu().numpy().squeeze()
            if len(flow_gt_np.shape) > 2:
                flow_gt_np = flow_gt_np[0]  # 取第一个通道
            
            # 归一化到0-255范围
            flow_gt_normalized = cv2.normalize(flow_gt_np, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
            
            # 应用Canny边缘检测
            edges = cv2.Canny(flow_gt_normalized, threshold1=50, threshold2=150)
            
            # 将边缘图变大一些，使用膨胀操作
            kernel = np.ones((3, 3), np.uint8)
            edges = cv2.dilate(edges, kernel, iterations=1)

            # 保存边缘图的
            # cv2.imwrite(f'edge_{i_batch}.png', edges)

            edges_tensor = torch.from_numpy(edges).flatten().bool().cuda()
            
            # 创建边缘和非边缘区域的mask
            edge_mask = val_basic & edges_tensor
            nonedge_mask = val_basic & (~edges_tensor)
            
            # 计算各区域的指标
            out_basic = (epe_single > 1.0).float()
            
            # Edge区域
            if edge_mask.sum() > 0:
                epe_list_edge.append(epe_single[edge_mask].mean().item())
                out_list_edge.append(out_basic[edge_mask].mean().item())
            
            # Non-Edge区域
            if nonedge_mask.sum() > 0:
                epe_list_nonedge.append(epe_single[nonedge_mask].mean().item())
                out_list_nonedge.append(out_basic[nonedge_mask].mean().item())

        if i_batch < 9 or (i_batch + 1) % 10 == 0:
            # 计算当前的平均值
            current_epe_overall = np.mean(epe_list_overall) if len(epe_list_overall) > 0 else 0.0
            current_d1_overall = 100 * np.mean(out_list_overall) if len(out_list_overall) > 0 else 0.0
            
            current_epe_edge = np.mean(epe_list_edge) if len(epe_list_edge) > 0 else 0.0
            current_d1_edge = 100 * np.mean(out_list_edge) if len(out_list_edge) > 0 else 0.0
            
            current_epe_nonedge = np.mean(epe_list_nonedge) if len(epe_list_nonedge) > 0 else 0.0
            current_d1_nonedge = 100 * np.mean(out_list_nonedge) if len(out_list_nonedge) > 0 else 0.0
            
            print(f"Scene Flow Iter {i_batch + 1}/{len(val_loader)} - "
                  f"Overall: EPE {round(current_epe_overall, 4)} D1 {round(current_d1_overall, 4)} | "
                  f"Edge: EPE {round(current_epe_edge, 4)} D1 {round(current_d1_edge, 4)} | "
                  f"Non-Edge: EPE {round(current_epe_nonedge, 4)} D1 {round(current_d1_nonedge, 4)}")

    # 计算最终结果
    epe_overall = np.mean(epe_list_overall) if len(epe_list_overall) > 0 else 0.0
    d1_overall = 100 * np.mean(out_list_overall) if len(out_list_overall) > 0 else 0.0
    
    epe_edge = np.mean(epe_list_edge) if len(epe_list_edge) > 0 else 0.0
    d1_edge = 100 * np.mean(out_list_edge) if len(out_list_edge) > 0 else 0.0
    
    epe_nonedge = np.mean(epe_list_nonedge) if len(epe_list_nonedge) > 0 else 0.0
    d1_nonedge = 100 * np.mean(out_list_nonedge) if len(out_list_nonedge) > 0 else 0.0

    # 输出结果
    print("\n" + "="*80)
    print("SCENE FLOW VALIDATION RESULTS:")
    print("="*80)
    print(f"Overall Region:   EPE {epe_overall:.4f}, D1 {d1_overall:.4f}")
    print(f"Edge Region:      EPE {epe_edge:.4f}, D1 {d1_edge:.4f}")
    print(f"Non-Edge Region:  EPE {epe_nonedge:.4f}, D1 {d1_nonedge:.4f}")
    print("="*80)
    
    # 写入到test.txt文件
    with open('test.txt', 'a') as f:
        f.write(f"Scene Flow (Edge Analysis) Validation Results:\n")
        if restore_ckpt:
            f.write(f"Model weights: {restore_ckpt}\n")
        f.write(f"Dataset: Scene Flow test set (frames_finalpass)\n")
        f.write(f"Iterations: {iters}, Mixed precision: {mixed_prec}\n")
        f.write(f"Overall Region:   EPE {epe_overall:.4f}, D1 {d1_overall:.4f}\n")
        f.write(f"Edge Region:      EPE {epe_edge:.4f}, D1 {d1_edge:.4f}\n")
        f.write(f"Non-Edge Region:  EPE {epe_nonedge:.4f}, D1 {d1_nonedge:.4f}\n")
        f.write("-" * 60 + "\n")
    
    return {
        'scene-flow-epe': epe_overall, 
        'scene-flow-d1': d1_overall,
        'scene-flow-edge-epe': epe_edge,
        'scene-flow-edge-d1': d1_edge,
        'scene-flow-nonedge-epe': epe_nonedge,
        'scene-flow-nonedge-d1': d1_nonedge
    }


@torch.no_grad()
def validate_sceneflow_occlusion(model, iters=32, mixed_prec=False, restore_ckpt=None):
    """ Peform validation using the Scene Flow (TEST) split with occlusion mask """

    class SelfOccluMask(nn.Module):
        def __init__(self, maxDisp=21, device='cuda'):
            super(SelfOccluMask, self).__init__()
            self.maxDisp = maxDisp
            self.device = device
            self.init_kernel()

        def init_kernel(self):
            self.convweights = torch.zeros(self.maxDisp, 1, 3, self.maxDisp + 2, device=self.device, dtype=torch.float32)
            self.occweights = torch.zeros(self.maxDisp, 1, 3, self.maxDisp + 2, device=self.device, dtype=torch.float32)
            self.convbias = (torch.arange(self.maxDisp, device=self.device, dtype=torch.float32) + 1)
            self.padding = nn.ReplicationPad2d((0, self.maxDisp + 1, 1, 1))
            for i in range(0, self.maxDisp):
                self.convweights[i, 0, :, 0:2] = 1 / 6
                self.convweights[i, 0, :, i + 2:i + 3] = -1 / 3
                self.occweights[i, 0, :, i + 2:i + 3] = 1 / 3

        def forward(self, dispmap, bsline=-1):
            maskl = self.computeMask(dispmap, 'l')
            maskr = self.computeMask(dispmap, 'r')
            lind = bsline < 0
            rind = bsline > 0
            mask = torch.zeros(dispmap.shape, dtype=maskl.dtype, device=self.device)
            mask[lind, :, :, :] = maskl[lind, :, :, :]
            mask[rind, :, :, :] = maskr[rind, :, :, :]
            return mask

        def computeMask(self, dispmap, direction):
            with torch.no_grad():
                if direction == 'l':
                    padmap = self.padding(dispmap)
                    output = nn.functional.conv2d(padmap, self.convweights, self.convbias)
                    output = torch.abs(output)
                    mask, min_idx = torch.min(output, dim=1, keepdim=True)
                    mask = mask.clamp(0, 1)
                elif direction == 'r':
                    dispmap_opp = torch.flip(dispmap, dims=[3])
                    padmap = self.padding(dispmap_opp)
                    output = nn.functional.conv2d(padmap, self.convweights,
                                                self.convbias)
                    output = torch.abs(output)
                    mask, min_idx = torch.min(output, dim=1, keepdim=True)
                    mask = mask.clamp(0, 1)
                    mask = torch.flip(mask, dims=[3])
            return mask

    model.eval()
    val_dataset = datasets.SceneFlowDatasets(dstype='frames_finalpass', things_test=True)
    val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=8, pin_memory=True, shuffle=False, num_workers=4, drop_last=False)

    # 初始化遮挡掩码计算器
    occlusion_mask_calculator = SelfOccluMask(maxDisp=192, device='cuda')
    
    # 三个区域的指标列表：Overall, Occluded, Non-Occluded
    out_list_overall, epe_list_overall = [], []
    out_list_occluded, epe_list_occluded = [], []
    out_list_nonoccluded, epe_list_nonoccluded = [], []
    
    for i_batch, (_, *data_blob) in enumerate(tqdm(val_loader)):
        image1, image2, flow_gt, occ_mask, valid_gt = [x.cuda() for x in data_blob]

        padder = InputPadder(image1.shape, divis_by=32)
        image1, image2 = padder.pad(image1, image2)

        with autocast(enabled=mixed_prec):
            flow_pr, _, _ = model(image1, image2, iters=iters, test_mode=True)
        flow_pr = padder.unpad(flow_pr)
        assert flow_pr.shape == flow_gt.shape, (flow_pr.shape, flow_gt.shape)

        epe = torch.abs(flow_pr - flow_gt)
        
        # 从ground truth视差图计算遮挡掩码
        occlusion_mask_batch = occlusion_mask_calculator(flow_gt, bsline=-1)
        
        # 计算左侧遮挡区域（没有匹配像素的区域）
        batch_size, _, H, W = flow_gt.shape
        left_occlusion_mask_batch = torch.zeros_like(occlusion_mask_batch)
        
        for b in range(batch_size):
            disparity_map = flow_gt[b:b+1]  # [1, 1, H, W]
            
            # 创建网格坐标
            meshgrid_x, meshgrid_y = torch.meshgrid(
                torch.arange(W, device=flow_gt.device),
                torch.arange(H, device=flow_gt.device), indexing='xy')
            meshgrid_x = meshgrid_x.float()  # [H, W]
            meshgrid_y = meshgrid_y.float()  # [H, W]

            # 调整网格坐标，考虑视差
            # 右图像中的像素映射到左图像的坐标，需要减去视差
            grid_x = meshgrid_x - disparity_map.squeeze(1)  # [1, H, W]
            grid_y = meshgrid_y.unsqueeze(0)  # [1, H, W]

            # 将网格坐标归一化到 [-1, 1]
            grid_x_normalized = 2.0 * grid_x / (W - 1) - 1.0  # [1, H, W]
            grid_y_normalized = 2.0 * grid_y / (H - 1) - 1.0  # [1, H, W]

            # 合并网格坐标
            grid = torch.stack((grid_x_normalized, grid_y_normalized), dim=3)  # [1, H, W, 2]

            # 创建一个虚拟的右图像（全1）用于检测有效区域
            dummy_right = torch.ones(1, 1, H, W, device=flow_gt.device)
            
            # 对虚拟右图像进行采样
            warped_right = torch.nn.functional.grid_sample(dummy_right, grid, align_corners=True, mode='bilinear', padding_mode='zeros')

            # 左侧遮挡掩码：采样结果为0的区域就是遮挡区域
            left_occlusion_mask_batch[b] = (warped_right.squeeze() == 0).float().unsqueeze(0)
        
       
        occlusion_threshold = 0.5
        self_occluded_pixels_batch = (occlusion_mask_batch < occlusion_threshold).float()
        left_occluded_pixels_batch = (left_occlusion_mask_batch > 0.5).float()
        
        # 最终的遮挡掩码：自遮挡 OR 左侧边界遮挡
        occluded_pixels_batch = (self_occluded_pixels_batch + left_occluded_pixels_batch) > 0.5
        
        # 保存前几张遮挡掩码图像用于检查
        if i_batch == 0:  # 只保存第一个batch的图像
            import os
            os.makedirs('occlusion_masks', exist_ok=True)
            
            for save_idx in range(min(4, batch_size)):  # 保存前4张图像
                # 保存SelfOccluMask结果
                self_mask_img = (self_occluded_pixels_batch[save_idx, 0].cpu().numpy() * 255).astype(np.uint8)
                cv2.imwrite(f'occlusion_masks/self_occlusion_mask_{save_idx}.png', self_mask_img)
                
                # 保存左侧边界遮挡结果
                left_mask_img = (left_occluded_pixels_batch[save_idx, 0].cpu().numpy() * 255).astype(np.uint8)
                cv2.imwrite(f'occlusion_masks/left_occlusion_mask_{save_idx}.png', left_mask_img)
                
                # 保存最终合并的遮挡掩码
                final_mask_img = (occluded_pixels_batch[save_idx, 0].cpu().numpy() * 255).astype(np.uint8)
                cv2.imwrite(f'occlusion_masks/final_occlusion_mask_{save_idx}.png', final_mask_img)
                
                # 保存ground truth视差图用于对比
                disp_img = flow_gt[save_idx, 0].cpu().numpy()
                disp_img_normalized = cv2.normalize(disp_img, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
                cv2.imwrite(f'occlusion_masks/disparity_gt_{save_idx}.png', disp_img_normalized)
        
        # 计算Overall区域的指标（与validate_sceneflow_origin保持一致）
        epe_flat = epe.flatten()
        val_overall = (valid_gt.flatten() >= 0.5) & (flow_gt.abs().flatten() < 192)
        out_overall = (epe_flat > 1.0).float()
        epe_list_overall.append(epe_flat[val_overall].mean().item())
        out_list_overall.append(out_overall[val_overall].mean().item())
        
        # 对batch中的每个样本进行处理（用于计算遮挡区域）
        batch_size = flow_gt.shape[0]
        
        for b in range(batch_size):
            # 获取单个样本的数据
            epe_single = epe[b].flatten()
            flow_gt_single = flow_gt[b]
            valid_gt_single = valid_gt[b]
            occluded_pixels = occluded_pixels_batch[b].flatten()
            
            # 基本的有效性mask
            val_basic = (valid_gt_single.flatten() >= 0.5) & (flow_gt_single.abs().flatten() < 192)
            
            # 创建遮挡和非遮挡区域的mask
            occluded_mask = val_basic & occluded_pixels
            nonoccluded_mask = val_basic & (~occluded_pixels)
            
            # 计算各区域的指标
            out_basic = (epe_single > 1.0).float()
            
            # Occluded区域
            if occluded_mask.sum() > 0:
                epe_list_occluded.append(epe_single[occluded_mask].mean().item())
                out_list_occluded.append(out_basic[occluded_mask].mean().item())
            
            # Non-Occluded区域
            if nonoccluded_mask.sum() > 0:
                epe_list_nonoccluded.append(epe_single[nonoccluded_mask].mean().item())
                out_list_nonoccluded.append(out_basic[nonoccluded_mask].mean().item())

        if i_batch < 9 or (i_batch + 1) % 10 == 0:
            # 计算当前的平均值
            current_epe_overall = np.mean(epe_list_overall) if len(epe_list_overall) > 0 else 0.0
            current_d1_overall = 100 * np.mean(out_list_overall) if len(out_list_overall) > 0 else 0.0
            
            current_epe_occluded = np.mean(epe_list_occluded) if len(epe_list_occluded) > 0 else 0.0
            current_d1_occluded = 100 * np.mean(out_list_occluded) if len(out_list_occluded) > 0 else 0.0
            
            current_epe_nonoccluded = np.mean(epe_list_nonoccluded) if len(epe_list_nonoccluded) > 0 else 0.0
            current_d1_nonoccluded = 100 * np.mean(out_list_nonoccluded) if len(out_list_nonoccluded) > 0 else 0.0
            
            print(f"Scene Flow Iter {i_batch + 1}/{len(val_loader)} - "
                  f"Overall: EPE {round(current_epe_overall, 4)} D1 {round(current_d1_overall, 4)} | "
                  f"Occluded: EPE {round(current_epe_occluded, 4)} D1 {round(current_d1_occluded, 4)} | "
                  f"Non-Occluded: EPE {round(current_epe_nonoccluded, 4)} D1 {round(current_d1_nonoccluded, 4)}")

    # 计算最终结果
    epe_overall = np.mean(epe_list_overall) if len(epe_list_overall) > 0 else 0.0
    d1_overall = 100 * np.mean(out_list_overall) if len(out_list_overall) > 0 else 0.0
    
    epe_occluded = np.mean(epe_list_occluded) if len(epe_list_occluded) > 0 else 0.0
    d1_occluded = 100 * np.mean(out_list_occluded) if len(out_list_occluded) > 0 else 0.0
    
    epe_nonoccluded = np.mean(epe_list_nonoccluded) if len(epe_list_nonoccluded) > 0 else 0.0
    d1_nonoccluded = 100 * np.mean(out_list_nonoccluded) if len(out_list_nonoccluded) > 0 else 0.0

    # 输出结果
    print("\n" + "="*80)
    print("SCENE FLOW OCCLUSION VALIDATION RESULTS:")
    print("="*80)
    print(f"Overall Region:      EPE {epe_overall:.4f}, D1 {d1_overall:.4f}")
    print(f"Occluded Region:     EPE {epe_occluded:.4f}, D1 {d1_occluded:.4f}")
    print(f"Non-Occluded Region: EPE {epe_nonoccluded:.4f}, D1 {d1_nonoccluded:.4f}")
    print("="*80)
    
    # 写入到test.txt文件
    with open('test.txt', 'a') as f:
        f.write(f"Scene Flow (Occlusion Analysis) Validation Results:\n")
        if restore_ckpt:
            f.write(f"Model weights: {restore_ckpt}\n")
        f.write(f"Dataset: Scene Flow test set (frames_finalpass)\n")
        f.write(f"Iterations: {iters}, Mixed precision: {mixed_prec}\n")
        f.write(f"Overall Region:      EPE {epe_overall:.4f}, D1 {d1_overall:.4f}\n")
        f.write(f"Occluded Region:     EPE {epe_occluded:.4f}, D1 {d1_occluded:.4f}\n")
        f.write(f"Non-Occluded Region: EPE {epe_nonoccluded:.4f}, D1 {d1_nonoccluded:.4f}\n")
        f.write("-" * 60 + "\n")
    
    return {
        'scene-flow-epe': epe_overall, 
        'scene-flow-d1': d1_overall,
        'scene-flow-occluded-epe': epe_occluded,
        'scene-flow-occluded-d1': d1_occluded,
        'scene-flow-nonoccluded-epe': epe_nonoccluded,
        'scene-flow-nonoccluded-d1': d1_nonoccluded
    }




@torch.no_grad()
def validate_middlebury(model, iters=32, split='MiddEval3', mixed_prec=False, resolution='F', restore_ckpt=None):
    """ Peform validation using the Middlebury-V3 dataset """
    model.eval()
    aug_params = {}
    val_dataset = datasets.Middlebury(aug_params, split=split, resolution=resolution)

    out_list_noc, epe_list_noc = [], []  # non-occluded
    out_list_occ, epe_list_occ = [], []  # occluded
    out_list_all, epe_list_all = [], []  # all areas
    
    for val_id in tqdm(range(len(val_dataset)), desc=f"Middlebury{resolution} ({split}) Validation"):
        (imageL_file, _, _), image1, image2, flow_gt, valid_gt = val_dataset[val_id]
        image1 = image1[None].cuda()
        image2 = image2[None].cuda()

        padder = InputPadder(image1.shape, divis_by=32)
        image1, image2 = padder.pad(image1, image2)

        with autocast(enabled=mixed_prec):
            flow_pr, _, _ = model(image1, image2, iters=iters, test_mode=True)
        flow_pr = padder.unpad(flow_pr).cpu().squeeze(0)

        assert flow_pr.shape == flow_gt.shape, (flow_pr.shape, flow_gt.shape)
        # epe = torch.sum((flow_pr - flow_gt)**2, dim=0).sqrt()
        epe = torch.abs(flow_pr - flow_gt)
        epe_flattened = epe.flatten()

        occ_mask = Image.open(imageL_file.replace('im0.png', 'mask0nocc.png')).convert('L')
        occ_mask = np.ascontiguousarray(occ_mask, dtype=np.float32).flatten()

        # Define masks for different regions
        valid_base = (valid_gt.reshape(-1) >= 0.5) & (flow_gt.abs().reshape(-1) < 1000)
        
        # Non-occluded region (white in mask, value == 255)
        val_noc = valid_base & (occ_mask == 255)
        
        # Occluded region (gray in mask, value == 128)
        val_occ = valid_base & (occ_mask == 128)
        
        # All valid regions (excluding invalid pixels with value 0)
        val_all = valid_base & (occ_mask > 0)
        
        out = (epe_flattened > 3.0)
        
        # Calculate metrics for non-occluded regions
        if val_noc.sum() > 0:
            image_out_noc = out[val_noc].float().mean().item()
            image_epe_noc = epe_flattened[val_noc].mean().item()
            epe_list_noc.append(image_epe_noc)
            out_list_noc.append(image_out_noc)
        else:
            image_epe_noc = float('inf')
            image_out_noc = 1.0
        
        # Calculate metrics for occluded regions
        if val_occ.sum() > 0:
            image_out_occ = out[val_occ].float().mean().item()
            image_epe_occ = epe_flattened[val_occ].mean().item()
            epe_list_occ.append(image_epe_occ)
            out_list_occ.append(image_out_occ)
        else:
            image_epe_occ = float('inf')
            image_out_occ = 1.0
        
        # Calculate metrics for all regions
        if val_all.sum() > 0:
            image_out_all = out[val_all].float().mean().item()
            image_epe_all = epe_flattened[val_all].mean().item()
            epe_list_all.append(image_epe_all)
            out_list_all.append(image_out_all)
        else:
            image_epe_all = float('inf')
            image_out_all = 1.0

        logging.info(f"Middlebury Iter {val_id+1} out of {len(val_dataset)}. NOC: EPE {round(image_epe_noc,4)} D1 {round(image_out_noc,4)} | OCC: EPE {round(image_epe_occ,4)} D1 {round(image_out_occ,4)} | ALL: EPE {round(image_epe_all,4)} D1 {round(image_out_all,4)}")

    # Calculate final metrics
    epe_noc = np.mean(epe_list_noc) if epe_list_noc else float('inf')
    d1_noc = 100 * np.mean(out_list_noc) if out_list_noc else 100.0
    
    epe_occ = np.mean(epe_list_occ) if epe_list_occ else float('inf')
    d1_occ = 100 * np.mean(out_list_occ) if out_list_occ else 100.0
    
    epe_all = np.mean(epe_list_all) if epe_list_all else float('inf')
    d1_all = 100 * np.mean(out_list_all) if out_list_all else 100.0

    print(f"Validation Middlebury{resolution}:")
    print(f"  Non-occluded: EPE {epe_noc:.4f}, D1 {d1_noc:.2f}%")
    print(f"  Occluded: EPE {epe_occ:.4f}, D1 {d1_occ:.2f}%") 
    print(f"  All: EPE {epe_all:.4f}, D1 {d1_all:.2f}%")
    
    # Write results to test.txt file
    with open('test.txt', 'a') as f:
        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Middlebury{resolution} ({split}) Validation Results:\n")
        if restore_ckpt:
            f.write(f"Model weights: {restore_ckpt}\n")
        f.write(f"Dataset: Middlebury {split} resolution {resolution}\n")
        f.write(f"Iterations: {iters}, Mixed precision: {mixed_prec}\n")
        f.write(f"Non-occluded: EPE: {epe_noc:.4f}, D1: {d1_noc:.2f}%\n")
        f.write(f"Occluded: EPE: {epe_occ:.4f}, D1: {d1_occ:.2f}%\n")
        f.write(f"All: EPE: {epe_all:.4f}, D1: {d1_all:.2f}%\n")
        f.write("-" * 60 + "\n")
    
    return {
        f'middlebury{split}-noc-epe': epe_noc, f'middlebury{split}-noc-d1': d1_noc,
        f'middlebury{split}-occ-epe': epe_occ, f'middlebury{split}-occ-d1': d1_occ,
        f'middlebury{split}-all-epe': epe_all, f'middlebury{split}-all-d1': d1_all
    }


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--restore_ckpt', help="restore checkpoint", default="checkpoints/sceneflow/98000.pth")
    parser.add_argument('--dataset', help="dataset for evaluation", default='sceneflow', choices=["eth3d", "kitti", "sceneflow", "middlebury", "drivingstereo"])
    parser.add_argument('--mixed_precision', default=False, help='use mixed precision')
    parser.add_argument("--precision_dtype",default="float32",choices=["float16", "bfloat16", "float32"],help="Choose precision type: float16 or bfloat16 or float32")
    parser.add_argument('--valid_iters', type=int, default=32, help='number of flow-field updates during forward pass')
    parser.add_argument('--sceneflow_eval_type', default='origin', choices=['edge', 'occlusion', 'origin', 'both'], help='Scene Flow evaluation type: edge, occlusion, origin, or both')
    parser.add_argument('--driving_scene', default='all', choices=['all', 'sunny', 'rainy', 'cloudy', 'foggy'], help='DrivingStereo scene for evaluation: all, sunny, rainy, cloudy, or foggy')

    # Architecure choices
    parser.add_argument('--encoder', type=str, default='vitl', choices=['vits', 'vitb', 'vitl', 'vitg'])
    parser.add_argument('--hidden_dims', nargs='+', type=int, default=[128]*3, help="hidden state and context dimensions")
    parser.add_argument('--corr_implementation', choices=["reg", "alt", "reg_cuda", "alt_cuda"], default="reg", help="correlation volume implementation")
    parser.add_argument('--shared_backbone', action='store_true', help="use a single backbone for the context and feature encoders")
    parser.add_argument('--corr_levels', type=int, default=2, help="number of levels in the correlation pyramid")
    parser.add_argument('--corr_radius', type=int, default=4, help="width of the correlation pyramid")
    parser.add_argument('--n_downsample', type=int, default=2, help="resolution of the disparity field (1/2^K)")
    parser.add_argument('--slow_fast_gru', action='store_true', help="iterate the low-res GRUs more frequently")
    parser.add_argument('--n_gru_layers', type=int, default=3, help="number of hidden GRU levels")
    parser.add_argument('--max_disp', type=int, default=192, help="max disp of geometry encoding volume")
    args = parser.parse_args()
    
    model = IGEVStereo(args)

    if args.restore_ckpt is not None:
        assert args.restore_ckpt.endswith(".pth")
        logging.info("Loading checkpoint...")
        model.load_state_dict(torch.load(args.restore_ckpt, map_location='cpu'), strict=True)
        logging.info(f"Done loading checkpoint")

    model = torch.nn.DataParallel(model, device_ids=[0])

    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s %(levelname)-8s [%(filename)s:%(lineno)d] %(message)s')

    model.cuda()
    model.eval()
    if hasattr(model, 'module'):
        model.module.freeze_bn()
    else:
        model.freeze_bn()

    print(f"The model has {format(count_parameters(model)/1e6, '.2f')}M learnable parameters.")
    use_mixed_precision = args.corr_implementation.endswith("_cuda")

    if args.dataset == 'eth3d':
        validate_eth3d(model, iters=args.valid_iters, mixed_prec=use_mixed_precision, restore_ckpt=args.restore_ckpt)

    if args.dataset == 'kitti':
        validate_kitti(model, iters=22, mixed_prec=use_mixed_precision, year=2012, restore_ckpt=args.restore_ckpt)
        validate_kitti(model, iters=22, mixed_prec=use_mixed_precision, year=2015, restore_ckpt=args.restore_ckpt)

    if args.dataset == 'middlebury':
        validate_middlebury(model, iters=64, mixed_prec=use_mixed_precision, resolution='F', restore_ckpt=args.restore_ckpt)
        validate_middlebury(model, iters=32, mixed_prec=use_mixed_precision, resolution='H', restore_ckpt=args.restore_ckpt)
        validate_middlebury(model, iters=22, mixed_prec=use_mixed_precision, resolution='Q', restore_ckpt=args.restore_ckpt)

    if args.dataset == 'sceneflow':
        if args.sceneflow_eval_type in ['edge', 'both']:
            validate_sceneflow_edge(model, iters=args.valid_iters, mixed_prec=use_mixed_precision, restore_ckpt=args.restore_ckpt)
        if args.sceneflow_eval_type in ['occlusion', 'both']:
            validate_sceneflow_occlusion(model, iters=args.valid_iters, mixed_prec=use_mixed_precision, restore_ckpt=args.restore_ckpt)
        if args.sceneflow_eval_type in ['origin', 'both']:
            validate_sceneflow_origin(model, iters=args.valid_iters, mixed_prec=use_mixed_precision, restore_ckpt=args.restore_ckpt)

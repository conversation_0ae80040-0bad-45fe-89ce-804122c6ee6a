Using device: cuda
============================================================
DINOv3 Multi-Scale Feature Extraction
============================================================

1. Building Feature_extractor module (loads backbone + decoder)...
Building DINOv3 ViT-L/16 backbone and loading weights from: dinov3/checkpoint/dinov3_vitl16_pretrain_lvd1689m-8aa4cbdd.pth
✅ Backbone weights loaded.

2. Testing multi-scale feature extraction...
   Fused multi-scale features (1/4,1/8,1/16,1/32):
   Level 1 (1/4): torch.Size([1, 256, 512, 1024])
   Level 2 (1/8): torch.Size([1, 256, 256, 512])
   Level 3 (1/16): torch.Size([1, 256, 128, 256])
   Level 4 (1/32): torch.Size([1, 256, 64, 128])

✅ Multi-scale feature extraction completed successfully!

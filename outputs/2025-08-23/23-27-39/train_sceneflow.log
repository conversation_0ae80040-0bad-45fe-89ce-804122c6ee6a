[2025-08-23 23:27:49,418][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-08-23 23:27:52,771][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-08-23 23:27:52,898][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-08-23 23:27:52,898][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-08-23 23:27:54,921][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-08-23 23:27:55,439][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-08-23 23:27:55,651][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-08-23 23:27:55,656][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-08-23 23:28:00,274][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-08-23 23:28:00,274][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-08-23 23:28:00,277][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-08-23 23:28:00,278][root][INFO] - Adding 67158 samples from SceneFlow
[2025-08-23 23:28:00,280][root][INFO] - Adding 67158 samples from SceneFlow
[2025-08-23 23:28:00,283][root][INFO] - Adding 67158 samples from SceneFlow
[2025-08-23 23:28:00,283][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-08-23 23:28:00,306][root][INFO] - Adding 67158 samples from SceneFlow
[2025-08-23 23:28:00,658][dinov3][INFO] - using base=100 for rope new
[2025-08-23 23:28:00,659][dinov3][INFO] - using min_period=None for rope new
[2025-08-23 23:28:00,660][dinov3][INFO] - using max_period=None for rope new
[2025-08-23 23:28:00,661][dinov3][INFO] - using normalize_coords=separate for rope new
[2025-08-23 23:28:00,662][dinov3][INFO] - using shift_coords=None for rope new
[2025-08-23 23:28:00,662][dinov3][INFO] - using rescale_coords=2 for rope new
[2025-08-23 23:28:00,662][dinov3][INFO] - using jitter_coords=None for rope new
[2025-08-23 23:28:00,663][dinov3][INFO] - using dtype=fp32 for rope new
[2025-08-23 23:28:00,664][dinov3][INFO] - using mlp layer as FFN
[2025-08-23 23:28:00,664][dinov3][INFO] - using base=100 for rope new
[2025-08-23 23:28:00,665][dinov3][INFO] - using min_period=None for rope new
[2025-08-23 23:28:00,665][dinov3][INFO] - using max_period=None for rope new
[2025-08-23 23:28:00,666][dinov3][INFO] - using normalize_coords=separate for rope new
[2025-08-23 23:28:00,666][dinov3][INFO] - using shift_coords=None for rope new
[2025-08-23 23:28:00,667][dinov3][INFO] - using rescale_coords=2 for rope new
[2025-08-23 23:28:00,667][dinov3][INFO] - using jitter_coords=None for rope new
[2025-08-23 23:28:00,667][dinov3][INFO] - using dtype=fp32 for rope new
[2025-08-23 23:28:00,668][dinov3][INFO] - using mlp layer as FFN
[2025-08-23 23:28:00,672][dinov3][INFO] - using base=100 for rope new
[2025-08-23 23:28:00,673][dinov3][INFO] - using min_period=None for rope new
[2025-08-23 23:28:00,673][dinov3][INFO] - using max_period=None for rope new
[2025-08-23 23:28:00,674][dinov3][INFO] - using normalize_coords=separate for rope new
[2025-08-23 23:28:00,674][dinov3][INFO] - using shift_coords=None for rope new
[2025-08-23 23:28:00,674][dinov3][INFO] - using rescale_coords=2 for rope new
[2025-08-23 23:28:00,675][dinov3][INFO] - using jitter_coords=None for rope new
[2025-08-23 23:28:00,675][dinov3][INFO] - using dtype=fp32 for rope new
[2025-08-23 23:28:00,676][dinov3][INFO] - using mlp layer as FFN
[2025-08-23 23:28:00,826][dinov3][INFO] - using base=100 for rope new
[2025-08-23 23:28:00,826][dinov3][INFO] - using min_period=None for rope new
[2025-08-23 23:28:00,827][dinov3][INFO] - using max_period=None for rope new
[2025-08-23 23:28:00,827][dinov3][INFO] - using normalize_coords=separate for rope new
[2025-08-23 23:28:00,827][dinov3][INFO] - using shift_coords=None for rope new
[2025-08-23 23:28:00,828][dinov3][INFO] - using rescale_coords=2 for rope new
[2025-08-23 23:28:00,828][dinov3][INFO] - using jitter_coords=None for rope new
[2025-08-23 23:28:00,829][dinov3][INFO] - using dtype=fp32 for rope new
[2025-08-23 23:28:00,829][dinov3][INFO] - using mlp layer as FFN

{"time":"2025-06-17T17:38:36.282092021+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.9","symlink path":"/storage/pmj/zhi/project/MonSter_origin/wandb/offline-run-20250617_173836-ys9d807u/logs/debug-core.log"}
{"time":"2025-06-17T17:38:36.387936011+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-06-17T17:38:36.390426436+08:00","level":"INFO","msg":"created new stream","id":"ys9d807u"}
{"time":"2025-06-17T17:38:36.390774552+08:00","level":"INFO","msg":"stream: started","id":"ys9d807u"}
{"time":"2025-06-17T17:38:36.39078799+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"ys9d807u"}
{"time":"2025-06-17T17:38:36.39080897+08:00","level":"INFO","msg":"sender: started","stream_id":"ys9d807u"}
{"time":"2025-06-17T17:38:36.390855904+08:00","level":"INFO","msg":"handler: started","stream_id":"ys9d807u"}
{"time":"2025-06-17T17:38:36.402005166+08:00","level":"INFO","msg":"Starting system monitor"}

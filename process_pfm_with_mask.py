#!/usr/bin/env python3
"""
Script to process PFM disparity files with JPG mask files.
- Reads PFM files and corresponding JPG mask files
- Resizes mask to match disparity dimensions
- Applies mask (black=invalid, white=valid) to set invalid regions to 0
- Saves processed disparity back to PFM file (overwrites original)
- Saves jet-mapped visualization as PNG
"""

import os
import glob
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
from pathlib import Path
import cv2


def read_pfm(file_path):
    """Read PFM file and return disparity array."""
    with open(file_path, 'rb') as f:
        # Read header
        header = f.readline().decode('utf-8').strip()
        if header not in ['PF', 'Pf']:
            raise ValueError(f"Invalid PFM file format: {header}")
        
        # Read dimensions
        dimensions = f.readline().decode('utf-8').strip().split()
        width, height = int(dimensions[0]), int(dimensions[1])
        
        # Read scale factor
        scale = float(f.readline().decode('utf-8').strip())
        
        # Read binary data
        data = np.frombuffer(f.read(), dtype=np.float32)
        
        # Reshape and flip vertically (PFM stores bottom-to-top)
        disparity = data.reshape((height, width))
        if scale < 0:
            disparity = np.flip(disparity, axis=0)
        
        return disparity


def write_pfm(file_path, disparity):
    """Write disparity array to PFM file."""
    height, width = disparity.shape
    
    with open(file_path, 'wb') as f:
        # Write header
        f.write(b'Pf\n')
        f.write(f'{width} {height}\n'.encode('utf-8'))
        f.write(b'-1\n')
        
        # Flip vertically and write binary data
        flipped_disparity = np.flip(disparity, axis=0).astype(np.float32)
        f.write(flipped_disparity.tobytes())


def load_mask(mask_path, target_height, target_width):
    """Load JPG mask and resize to target dimensions."""
    # Load mask image
    mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    if mask is None:
        raise ValueError(f"Cannot load mask image: {mask_path}")
    
    # Resize mask to match disparity dimensions
    mask_resized = cv2.resize(mask, (target_width, target_height), interpolation=cv2.INTER_NEAREST)
    
    # Convert to binary mask (white=1, black=0)
    # Threshold at 128 to handle potential anti-aliasing
    binary_mask = (mask_resized > 128).astype(np.float32)
    
    return binary_mask


def save_jet_visualization(disparity, output_path):
    """Save disparity visualization with jet colormap."""
    # Normalize disparity for visualization (ignore zero values)
    valid_disp = disparity[disparity > 0]
    if len(valid_disp) > 0:
        vmin, vmax = np.percentile(valid_disp, [2, 98])  # Use percentiles for better visualization
    else:
        vmin, vmax = 0, 1
    
    # Create visualization
    plt.figure(figsize=(12, 8))
    plt.imshow(disparity, cmap='jet', vmin=vmin, vmax=vmax)
    plt.colorbar(label='Disparity')
    plt.axis('off')
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight', pad_inches=0)
    plt.close()


def process_folder(folder_path):
    """Process all PFM and JPG files in a folder."""
    print(f"Processing folder: {folder_path}")
    
    # Find PFM and JPG files
    pfm_files = glob.glob(os.path.join(folder_path, "*.pfm"))
    jpg_files = glob.glob(os.path.join(folder_path, "*.jpg"))
    
    if not pfm_files:
        print(f"  No PFM files found in {folder_path}")
        return
    
    if not jpg_files:
        print(f"  No JPG files found in {folder_path}")
        return
    
    # Process each PFM file
    for pfm_file in pfm_files:
        print(f"  Processing: {pfm_file}")
        
        try:
            # Read disparity
            disparity = read_pfm(pfm_file)
            height, width = disparity.shape
            
            # Process each mask file in the folder
            for jpg_file in jpg_files:
                print(f"    Applying mask: {jpg_file}")
                
                # Load and resize mask
                mask = load_mask(jpg_file, height, width)
                
                # Apply mask to disparity
                masked_disparity = disparity * mask
                
                # Create output filename with mask info
                pfm_stem = Path(pfm_file).stem
                jpg_stem = Path(jpg_file).stem
                
                # Save processed PFM (overwrite original)
                write_pfm(pfm_file, masked_disparity)
                print(f"    Updated PFM file: {pfm_file}")
                
                # Save jet visualization
                vis_output = os.path.join(folder_path, f"{pfm_stem}_masked_{jpg_stem}.png")
                save_jet_visualization(masked_disparity, vis_output)
                print(f"    Saved visualization: {vis_output}")
                
        except Exception as e:
            print(f"    Error processing {pfm_file}: {str(e)}")

def main():
    """Main function to process all folders."""
    # Base directory containing the folders
    base_dir = "/storage/pmj/zhi/project/MonSter_origin/test_output/middlebury"
    
    if not os.path.exists(base_dir):
        print(f"Base directory does not exist: {base_dir}")
        return
    
    # Find all subdirectories
    subdirs = [d for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d))]
    
    if not subdirs:
        print(f"No subdirectories found in {base_dir}")
        return
    
    print(f"Found {len(subdirs)} folders to process:")
    for subdir in subdirs:
        print(f"  - {subdir}")
    
    # Process each folder
    for subdir in subdirs:
        folder_path = os.path.join(base_dir, subdir)
        process_folder(folder_path)
    
    print("\nProcessing completed!")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Usage example showing how to use the modified Feature_extractor 
as a replacement for selective_igev's infer_mono method.
"""

import torch
import sys
import os

# Add paths
sys.path.append('/storage/pmj/zhi/project/dssm')
sys.path.append('/storage/pmj/zhi/project/dssm/dinov3-main')

def example_usage():
    """Example of how to use the modified Feature_extractor."""
    
    # Import the modified Feature_extractor
    from dinov3-main.feature_extractor import Feature_extractor
    
    # Initialize the feature extractor (replace with your actual checkpoint path)
    checkpoint_path = "checkpoint/dinov3_vitl16_pretrain_lvd1689m-8aa4cbdd.pth"
    
    # Check if checkpoint exists
    if not os.path.exists(checkpoint_path):
        print(f"Checkpoint not found at {checkpoint_path}")
        print("Please update the checkpoint_path to point to your DINOv3 checkpoint.")
        return
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # Initialize feature extractor
    feature_extractor = Feature_extractor(
        checkpoint_path=checkpoint_path,
        layer_indices=[4, 11, 17, 23],  # DINOv3 ViT-L layers
        features=256,
        device=device
    )
    feature_extractor.to(device)
    feature_extractor.eval()
    
    # Example stereo image pair (normalized to [-1, 1] range)
    batch_size = 1
    height, width = 512, 1024
    
    left_image = torch.randn(batch_size, 3, height, width, device=device) * 2 - 1
    right_image = torch.randn(batch_size, 3, height, width, device=device) * 2 - 1
    
    print(f"Input shapes: Left={left_image.shape}, Right={right_image.shape}")
    print(f"Input range: [{left_image.min():.3f}, {left_image.max():.3f}]")
    
    # Use the new infer_mono method (replacement for selective_igev's infer_mono)
    with torch.no_grad():
        left_features, right_features = feature_extractor.infer_mono(left_image, right_image)
    
    # The output format matches selective_igev's expected format:
    # - left_features: list of 4 feature maps at scales [1/4, 1/8, 1/16, 1/32]
    # - right_features: list of 4 feature maps at scales [1/4, 1/8, 1/16, 1/32]
    
    print("\nExtracted features:")
    scale_names = ["1/4", "1/8", "1/16", "1/32"]
    for i, (left_feat, right_feat) in enumerate(zip(left_features, right_features)):
        print(f"Scale {scale_names[i]}: Left={left_feat.shape}, Right={right_feat.shape}")
    
    # These features can now be used directly in selective_igev's subsequent processing
    print("\n✅ Feature extraction completed successfully!")
    print("These features are ready to be used in selective_igev's downstream processing.")

def integration_in_selective_igev():
    """
    Example of how to integrate this into selective_igev.py
    """
    
    print("\nIntegration example for selective_igev.py:")
    print("=" * 50)
    
    integration_code = '''
# In your selective_igev.py class, replace the infer_mono method usage:

class SelectiveIGEV(nn.Module):
    def __init__(self, args):
        super().__init__()
        # ... existing initialization ...
        
        # Initialize the DINOv3 feature extractor
        self.feature_extractor = Feature_extractor(
            checkpoint_path="path/to/dinov3_checkpoint.pth",
            layer_indices=[4, 11, 17, 23],
            features=256
        )
    
    def forward(self, image1, image2, ...):
        # Replace the original infer_mono call:
        # left_features, right_features = self.infer_mono(image1, image2)
        
        # With the new DINOv3-based feature extraction:
        left_features, right_features = self.feature_extractor.infer_mono(image1, image2)
        
        # Continue with the rest of your selective_igev processing...
        # The feature format is exactly the same as before
        
        return your_result
'''
    
    print(integration_code)

if __name__ == "__main__":
    print("DINOv3 Feature Extractor Usage Example")
    print("=" * 50)
    
    try:
        example_usage()
        integration_in_selective_igev()
    except Exception as e:
        print(f"Error: {e}")
        print("\nNote: Make sure you have the correct checkpoint path and all dependencies installed.")

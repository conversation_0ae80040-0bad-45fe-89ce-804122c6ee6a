import sys
sys.path.append("core")

import argparse
import numpy as np
import torch
from pathlib import Path
from core.monster import Monster
from core.utils.utils import InputPadder
from PIL import Image
import matplotlib.pyplot as plt
import matplotlib.cm as cm
from matplotlib.colors import LinearSegmentedColormap
import os

DEVICE = "cuda"

def load_image(imfile):
    img = np.array(Image.open(imfile)).astype(np.uint8)[..., :3]
    img = torch.from_numpy(img).permute(2, 0, 1).float()
    return img[None].to(DEVICE)

class MonsterWithDeltaTracker(Monster):
    """继承Monster类，添加delta_disp追踪功能"""
    def forward(self, image1, image2, iters=12, flow_init=None, test_mode=False):
        # 调用父类的前向传播逻辑，但需要修改以记录delta_disp
        self.delta_disps = []  # 存储每次迭代的delta_disp
        self.disp_history = []  # 存储每次迭代的视差值
        
        # 复制父类的forward逻辑，但添加delta_disp记录
        image1 = (2 * (image1 / 255.0) - 1.0).contiguous()
        image2 = (2 * (image2 / 255.0) - 1.0).contiguous()
        
        with torch.autocast(device_type='cuda', dtype=torch.float32): 
            depth_mono, features_mono_left, features_mono_right = self.infer_mono(image1, image2)
        
        scale_factor = 0.25
        size = (int(depth_mono.shape[-2] * scale_factor), int(depth_mono.shape[-1] * scale_factor))
        disp_mono_4x = torch.nn.functional.interpolate(depth_mono, size=size, mode='bilinear', align_corners=False)
        
        features_left = self.feat_transfer(features_mono_left)
        features_right = self.feat_transfer(features_mono_right)
        
        stem_2x = self.stem_2(image1)
        stem_4x = self.stem_4(stem_2x)
        stem_8x = self.stem_8(stem_4x)
        stem_16x = self.stem_16(stem_8x)
        stem_2y = self.stem_2(image2)
        stem_4y = self.stem_4(stem_2y)
        
        stem_x_list = [stem_16x, stem_8x, stem_4x]
        features_left[0] = torch.cat((features_left[0], stem_4x), 1)
        features_right[0] = torch.cat((features_right[0], stem_4y), 1)
        
        match_left = self.desc(self.conv(features_left[0]))
        match_right = self.desc(self.conv(features_right[0]))
        
        from core.submodule import build_gwc_volume
        gwc_volume = build_gwc_volume(match_left, match_right, self.args.max_disp//4, 8)
        gwc_volume = self.corr_stem(gwc_volume)
        gwc_volume = self.corr_feature_att(gwc_volume, features_left[0])
        geo_encoding_volume = self.cost_agg(gwc_volume, features_left)
        
        prob = torch.nn.functional.softmax(self.classifier(geo_encoding_volume).squeeze(1), dim=1)
        from core.submodule import disparity_regression
        init_disp = disparity_regression(prob, self.args.max_disp//4)
        
        cnet_list = self.feat_transfer_cnet(features_mono_left, stem_x_list)
        net_list = [torch.tanh(x[0]) for x in cnet_list]
        inp_list = [torch.relu(x[1]) for x in cnet_list]
        inp_list = [list(conv(i).split(split_size=conv.out_channels//3, dim=1)) for i,conv in zip(inp_list, self.context_zqr_convs)]
        net_list_mono = [x.clone() for x in net_list]
        
        from core.geometry import Combined_Geo_Encoding_Volume
        geo_fn = Combined_Geo_Encoding_Volume(match_left.float(), match_right.float(), 
                                             geo_encoding_volume.float(), 
                                             radius=self.args.corr_radius, 
                                             num_levels=self.args.corr_levels)
        
        b, c, h, w = match_left.shape
        coords = torch.arange(w).float().to(match_left.device).reshape(1,1,w,1).repeat(b, h, 1, 1).contiguous()
        disp = init_disp
        
        # 主迭代循环
        for itr in range(iters):
            disp = disp.detach()
            if itr >= 1:
                disp_mono_4x = disp_mono_4x.detach()
            
            geo_feat = geo_fn(disp, coords)
            
            # 混合阶段的准备
            if itr > int(iters-8):
                if itr == int(iters-7):
                    bs, _, _, _ = disp.shape
                    for i in range(bs):
                        with torch.autocast(device_type='cuda', dtype=torch.float32):
                            from core.monster import compute_scale_shift
                            scale, shift = compute_scale_shift(disp_mono_4x[i].clone().squeeze(1).to(torch.float32), 
                                                              disp[i].clone().squeeze(1).to(torch.float32))
                        disp_mono_4x[i] = scale * disp_mono_4x[i] + shift
                
                from core.warp import disp_warp
                warped_right_mono = disp_warp(features_right[0], disp_mono_4x.clone().to(features_right[0].dtype))[0]
                flaw_mono = warped_right_mono - features_left[0]
                
                warped_right_stereo = disp_warp(features_right[0], disp.clone().to(features_right[0].dtype))[0]
                flaw_stereo = warped_right_stereo - features_left[0]
                geo_feat_mono = geo_fn(disp_mono_4x, coords)
            
            # 更新阶段
            if itr <= int(iters-8):
                net_list, mask_feat_4, delta_disp, _conf = self.update_block(net_list, inp_list, geo_feat, disp, 
                                                                      iter16=self.args.n_gru_layers==3, 
                                                                      iter08=self.args.n_gru_layers>=2)
            else:
                net_list, mask_feat_4, delta_disp = self.update_block_mix_stereo(net_list, inp_list, 
                                                                                 flaw_stereo, disp, geo_feat, 
                                                                                 flaw_mono, disp_mono_4x, geo_feat_mono, 
                                                                                 iter16=self.args.n_gru_layers==3, 
                                                                                 iter08=self.args.n_gru_layers>=2)
                
                net_list_mono, mask_feat_4_mono, delta_disp_mono = self.update_block_mix_mono(net_list_mono, inp_list, 
                                                                                              flaw_mono, disp_mono_4x, geo_feat_mono, 
                                                                                              flaw_stereo, disp, geo_feat, 
                                                                                              iter16=self.args.n_gru_layers==3, 
                                                                                              iter08=self.args.n_gru_layers>=2)
                disp_mono_4x = disp_mono_4x + delta_disp_mono
            
            # 记录delta_disp（4x分辨率）
            self.delta_disps.append(delta_disp.detach().cpu())
            
            # 更新视差
            disp = disp + delta_disp
            
            # 记录当前视差值（上采样到原始分辨率用于可视化）
            disp_up = self.upsample_disp(disp, mask_feat_4, stem_2x)
            self.disp_history.append(disp_up.detach().cpu())
        
        # 返回最终视差以及所有的delta历史
        if test_mode:
            return disp_up
        else:
            return disp_up, self.delta_disps, self.disp_history

def analyze_convergence(delta_disps, threshold=0.01):
    """分析每个像素的收敛情况"""
    # delta_disps: list of [B, 1, H, W] tensors
    B, _, H, W = delta_disps[0].shape
    
    # 初始化收敛时间图（-1表示未收敛）
    convergence_iter = np.full((B, H, W), -1, dtype=np.int32)
    
    # 累积残差图
    cumulative_delta = np.zeros((B, H, W), dtype=np.float32)
    
    # 记录每个像素的收敛迭代次数
    for itr, delta in enumerate(delta_disps):
        delta_np = np.abs(delta.numpy()).squeeze(1)  # [B, H, W]
        cumulative_delta += delta_np
        
        # 标记收敛的像素（残差小于阈值且之前未标记）
        converged = (delta_np < threshold) & (convergence_iter == -1)
        convergence_iter[converged] = itr
    
    # 未收敛的像素设为最大迭代次数
    convergence_iter[convergence_iter == -1] = len(delta_disps)
    
    return convergence_iter, cumulative_delta

def visualize_convergence_analysis(image_left, delta_disps, disp_history, output_path, file_stem):
    """生成收敛分析的可视化"""
    # 分析收敛情况
    convergence_iter, cumulative_delta = analyze_convergence(delta_disps)
    
    # 创建图形
    fig = plt.figure(figsize=(20, 15))
    
    # 1. 原始左图
    ax1 = plt.subplot(3, 3, 1)
    ax1.imshow(image_left)
    ax1.set_title('Left Image')
    ax1.axis('off')
    
    # 2. 最终视差图
    ax2 = plt.subplot(3, 3, 2)
    final_disp = disp_history[-1].squeeze().numpy()
    im2 = ax2.imshow(final_disp, cmap='jet')
    ax2.set_title('Final Disparity')
    ax2.axis('off')
    plt.colorbar(im2, ax=ax2, fraction=0.046)
    
    # 3. 收敛速度图（哪些区域收敛快）
    ax3 = plt.subplot(3, 3, 3)
    im3 = ax3.imshow(convergence_iter[0], cmap='hot', vmin=0, vmax=len(delta_disps))
    ax3.set_title(f'Convergence Speed (iterations needed)\nRed=Fast, Yellow=Slow')
    ax3.axis('off')
    plt.colorbar(im3, ax=ax3, fraction=0.046)
    
    # 4-9. 选择6个代表性迭代的delta_disp可视化
    iters_to_show = [0, 3, 7, 11, 15, min(19, len(delta_disps)-1)]
    for idx, itr in enumerate(iters_to_show):
        if itr < len(delta_disps):
            ax = plt.subplot(3, 3, idx + 4)
            delta_magnitude = np.abs(delta_disps[itr].squeeze().numpy())
            im = ax.imshow(delta_magnitude, cmap='viridis', vmin=0, vmax=0.5)
            ax.set_title(f'|Δdisp| at iter {itr}')
            ax.axis('off')
            if idx == 0:  # 只在第一个子图添加colorbar
                plt.colorbar(im, ax=ax, fraction=0.046)
    
    plt.tight_layout()
    
    # 保存主要分析图
    main_output = os.path.join(output_path, f"{file_stem}_convergence_analysis.png")
    plt.savefig(main_output, dpi=150, bbox_inches='tight')
    plt.close()
    
    # 生成收敛统计图
    fig2, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 统计每个迭代收敛的像素百分比
    convergence_histogram = []
    for i in range(len(delta_disps) + 1):
        percent = np.sum(convergence_iter[0] == i) / convergence_iter[0].size * 100
        convergence_histogram.append(percent)
    
    ax1.bar(range(len(convergence_histogram)), convergence_histogram)
    ax1.set_xlabel('Iteration')
    ax1.set_ylabel('Percentage of Pixels Converged (%)')
    ax1.set_title('Pixel Convergence Distribution')
    ax1.grid(True, alpha=0.3)
    
    # 累积收敛曲线
    cumulative_convergence = np.cumsum(convergence_histogram)
    ax2.plot(range(len(cumulative_convergence)), cumulative_convergence, 'b-', linewidth=2)
    ax2.set_xlabel('Iteration')
    ax2.set_ylabel('Cumulative Pixels Converged (%)')
    ax2.set_title('Cumulative Convergence Curve')
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 105)
    
    # 标注关键点
    for threshold_percent in [50, 80, 95]:
        iter_needed = np.argmax(cumulative_convergence >= threshold_percent)
        ax2.axhline(y=threshold_percent, color='r', linestyle='--', alpha=0.5)
        ax2.axvline(x=iter_needed, color='r', linestyle='--', alpha=0.5)
        ax2.text(iter_needed + 0.5, threshold_percent - 5, 
                f'{threshold_percent}% @ iter {iter_needed}', fontsize=9)
    
    plt.tight_layout()
    stats_output = os.path.join(output_path, f"{file_stem}_convergence_stats.png")
    plt.savefig(stats_output, dpi=150, bbox_inches='tight')
    plt.close()
    
    # 生成区域分析热力图
    fig3, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 将图像分成4个区域分析
    h, w = convergence_iter[0].shape
    regions = {
        'Top-Left': convergence_iter[0][:h//2, :w//2],
        'Top-Right': convergence_iter[0][:h//2, w//2:],
        'Bottom-Left': convergence_iter[0][h//2:, :w//2],
        'Bottom-Right': convergence_iter[0][h//2:, w//2:]
    }
    
    for idx, (region_name, region_data) in enumerate(regions.items()):
        ax = axes[idx//2, idx%2]
        im = ax.imshow(region_data, cmap='hot', vmin=0, vmax=len(delta_disps))
        ax.set_title(f'{region_name} Region\nAvg iterations: {region_data.mean():.1f}')
        ax.axis('off')
        plt.colorbar(im, ax=ax, fraction=0.046)
    
    plt.tight_layout()
    region_output = os.path.join(output_path, f"{file_stem}_region_analysis.png")
    plt.savefig(region_output, dpi=150, bbox_inches='tight')
    plt.close()
    
    return main_output, stats_output, region_output

def main(args):
    # 加载模型
    model = torch.nn.DataParallel(MonsterWithDeltaTracker(args), device_ids=[0])
    checkpoint = torch.load(args.restore_ckpt, weights_only=False)
    
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    elif 'state_dict' in checkpoint:
        state_dict = checkpoint['state_dict']
    else:
        state_dict = checkpoint
    
    # 处理state_dict的module前缀
    ckpt = dict()
    for key in state_dict:
        if key.startswith('module.'):
            ckpt[key] = state_dict[key]
        else:
            ckpt['module.' + key] = state_dict[key]
    
    model.load_state_dict(ckpt, strict=False)
    model = model.module
    model.to(DEVICE)
    model.eval()
    
    # 创建输出目录
    output_path = Path(args.output_directory)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 加载图像
    image1 = load_image(args.left_img)
    image2 = load_image(args.right_img)
    
    # Padding
    padder = InputPadder(image1.shape, divis_by=32)
    image1_padded, image2_padded = padder.pad(image1, image2)
    
    # 运行模型并获取delta历史
    with torch.no_grad():
        final_disp, delta_disps, disp_history = model(image1_padded, image2_padded, 
                                                      iters=args.valid_iters, 
                                                      test_mode=False)
    
    # Unpad结果
    delta_disps_unpadded = [padder.unpad(delta) for delta in delta_disps]
    disp_history_unpadded = [padder.unpad(disp) for disp in disp_history]
    
    # 准备可视化
    image1_np = np.array(Image.open(args.left_img))
    file_stem = Path(args.left_img).stem
    
    # 生成可视化
    main_fig, stats_fig, region_fig = visualize_convergence_analysis(
        image1_np, delta_disps_unpadded, disp_history_unpadded, 
        output_path, file_stem
    )
    
    print(f"分析完成！")
    print(f"主要分析图: {main_fig}")
    print(f"统计图表: {stats_fig}")
    print(f"区域分析: {region_fig}")
    
    # 打印一些统计信息
    convergence_iter, cumulative_delta = analyze_convergence(delta_disps_unpadded)
    print(f"\n收敛统计:")
    print(f"平均收敛迭代数: {convergence_iter[0].mean():.2f}")
    print(f"最快收敛区域: {convergence_iter[0].min()} 次迭代")
    print(f"最慢收敛区域: {convergence_iter[0].max()} 次迭代")
    print(f"50%像素收敛所需迭代: {np.median(convergence_iter[0]):.0f}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--left_img", required=True, help="path to left image")
    parser.add_argument("--right_img", required=True, help="path to right image")
    parser.add_argument("--restore_ckpt", required=True, help="restore checkpoint")
    parser.add_argument("--output_directory", default="convergence_analysis", help="directory to save output")
    parser.add_argument("--valid_iters", type=int, default=25, help="number of iterations to analyze")
    
    # Model parameters
    parser.add_argument('--encoder', type=str, default='vitl', choices=['vits', 'vitb', 'vitl', 'vitg'])
    parser.add_argument('--hidden_dims', nargs='+', type=int, default=[128]*3)
    parser.add_argument('--corr_levels', type=int, default=2)
    parser.add_argument('--corr_radius', type=int, default=4)
    parser.add_argument('--n_downsample', type=int, default=2)
    parser.add_argument('--n_gru_layers', type=int, default=3)
    parser.add_argument('--max_disp', type=int, default=192)
    
    args = parser.parse_args()
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"
    
    main(args)

# 脚本主体内容
print("Convergence visualization script") 
Using device: cuda
============================================================
DINOv3 Multi-Scale Feature Extraction
============================================================

1. Building Feature_extractor module (loads backbone + decoder)...
Building DINOv3 ViT-L/16 backbone and loading weights from: dinov3/checkpoint/dinov3_vitl16_pretrain_lvd1689m-8aa4cbdd.pth
✅ Backbone weights loaded.

2. Testing multi-scale feature extraction...
Traceback (most recent call last):
  File "/storage/pmj/zhi/project/dssm/dinov3/feature_extractor.py", line 259, in <module>
    main()
  File "/storage/pmj/zhi/project/dssm/dinov3/feature_extractor.py", line 249, in main
    pyramid = extractor(test_input)
              ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/storage/pmj/zhi/project/dssm/dinov3/feature_extractor.py", line 110, in forward
    intermediate = self.backbone.get_intermediate_layers(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/storage/pmj/zhi/project/dinov3-main/dinov3/models/vision_transformer.py", line 291, in get_intermediate_layers
    outputs = self._get_intermediate_layers_not_chunked(x, n)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/storage/pmj/zhi/project/dinov3-main/dinov3/models/vision_transformer.py", line 266, in _get_intermediate_layers_not_chunked
    x, (H, W) = self.prepare_tokens_with_masks(x)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/storage/pmj/zhi/project/dinov3-main/dinov3/models/vision_transformer.py", line 187, in prepare_tokens_with_masks
    x = self.patch_embed(x)
        ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/storage/pmj/zhi/project/dinov3-main/dinov3/layers/patch_embed.py", line 70, in forward
    x = self.proj(x)  # B C H W
        ^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/torch/nn/modules/conv.py", line 554, in forward
    return self._conv_forward(input, self.weight, self.bias)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/mambaforge/envs/sm/lib/python3.12/site-packages/torch/nn/modules/conv.py", line 549, in _conv_forward
    return F.conv2d(
           ^^^^^^^^^
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same

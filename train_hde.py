import os
import hydra
import torch
from tqdm import tqdm
import torch.optim as optim
from accelerate import Accelerator
from accelerate.utils import set_seed
from accelerate.logging import get_logger
from accelerate import DataLoaderConfiguration
from accelerate.utils import DistributedDataParallelKwargs
from pathlib import Path
from typing import Mapping

import torch.nn.functional as F
import core.stereo_datasets as datasets
from core.selective_igev import IGEVStereo
from core.submodule import context_upsample
from contextlib import nullcontext


def freeze_except_hde(model: IGEVStereo):
    # 冻结除HDE相关外的参数
    for p in model.parameters():
        p.requires_grad = False
    for m in [
        getattr(model, 'corr_stem', None),
        getattr(model, 'corr_feature_att', None),
        getattr(model, 'cost_agg', None),
        getattr(model, 'classifier', None),
        getattr(model, 'classifier_refine', None),
        getattr(model, 'occ_head', None),  # dustbin维度头
        getattr(model, 'match_proj', None),
    ]:
        if m is not None:
            for p in m.parameters():
                p.requires_grad = True


def compute_hde_outputs_fullres(model: IGEVStereo, image1: torch.Tensor, image2: torch.Tensor):
    # 归一化
    img1 = (2 * (image1 / 255.0) - 1.0).contiguous()
    img2 = (2 * (image2 / 255.0) - 1.0).contiguous()
    with torch.no_grad():
        feat_mono_l, feat_mono_r = model.infer_mono(img1, img2, use_ckpt=False)
        feats_l = model.feat_transfer(feat_mono_l)
        feats_r = model.feat_transfer(feat_mono_r)
        stem_2x = model.stem_2(img1)
        stem_4x = model.stem_4(stem_2x)
        stem_2y = model.stem_2(img2)
        stem_4y = model.stem_4(stem_2y)
        feats_l[0] = torch.cat((feats_l[0], stem_4x), 1)
        feats_r[0] = torch.cat((feats_r[0], stem_4y), 1)
    # HDE路径：确保可训练模块在有梯度下运行
    match_left = model.match_l2(model.match_proj(feats_l[0]))
    match_right = model.match_l2(model.match_proj(feats_r[0]))
    init_disp_low, conf_map = model.hierarchical_disparity_estimation(match_left, match_right, feats_l)
    # 使用spx进行全分辨率上采样（与forward一致）
    xspx = model.spx_4(feats_l[0])
    xspx = model.spx_2(xspx, stem_2x)
    spx_pred = model.spx(xspx)
    spx_pred = F.softmax(spx_pred, 1)
    init_disp_up = context_upsample(init_disp_low * 4.0, spx_pred).unsqueeze(1)
    return init_disp_up


@hydra.main(version_base=None, config_path='config', config_name='train_hde')
def main(cfg):
    set_seed(cfg.seed)
    logger = get_logger(__name__)
    Path(cfg.save_path).mkdir(exist_ok=True, parents=True)
    kwargs = DistributedDataParallelKwargs(find_unused_parameters=True)
    accelerator = Accelerator(mixed_precision='bf16', dataloader_config=DataLoaderConfiguration(use_seedable_sampler=True), kwargs_handlers=[kwargs], step_scheduler_with_optimizer=False)

    train_dataset = datasets.fetch_dataloader(cfg)
    if isinstance(train_dataset, torch.utils.data.DataLoader):
        train_loader = train_dataset
    else:
        assert train_dataset is not None, "fetch_dataloader returned None"
        train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=cfg.batch_size//cfg.num_gpu,
            pin_memory=True, shuffle=True, num_workers=int(4), drop_last=True)

    model = IGEVStereo(cfg)
    freeze_except_hde(model)

    if not cfg.restore_ckpt.endswith("None"):
        assert cfg.restore_ckpt.endswith(".pth")
        if int(os.environ.get('LOCAL_RANK', '0')) == 0:
            print(f"Loading checkpoint from {cfg.restore_ckpt}")
        assert os.path.exists(cfg.restore_ckpt)
        checkpoint = torch.load(cfg.restore_ckpt, map_location='cpu')
        state = checkpoint['state_dict'] if isinstance(checkpoint, dict) and 'state_dict' in checkpoint else checkpoint
        fixed = {k.replace('module.', ''): v for k, v in state.items()}
        try:
            model.load_state_dict(fixed, strict=True)
            if int(os.environ.get('LOCAL_RANK', '0')) == 0:
                print("Loaded checkpoint strictly (strict=True).")
        except Exception as e:
            missing = model.load_state_dict(fixed, strict=False)
            if int(os.environ.get('LOCAL_RANK', '0')) == 0:
                print(f"Strict load failed ({type(e).__name__}): {e}")
                print(f"Loaded checkpoint with strict=False. Missing/Unexpected: {missing}")
        # 若checkpoint未包含refine头权重，则复制classifier权重到classifier_refine
        has_refine = any(k.endswith('classifier_refine.weight') for k in fixed.keys())
        if (not has_refine) and hasattr(model, 'classifier_refine') and hasattr(model, 'classifier'):
            with torch.no_grad():
                if hasattr(model.classifier_refine, 'weight') and hasattr(model.classifier, 'weight'):
                    if isinstance(model.classifier_refine.weight, torch.Tensor) and isinstance(model.classifier.weight, torch.Tensor):
                        if model.classifier_refine.weight.shape == model.classifier.weight.shape:
                            model.classifier_refine.weight.copy_(model.classifier.weight)
                            if int(os.environ.get('LOCAL_RANK', '0')) == 0:
                                print('[HDE] Copied classifier weights into classifier_refine (script-level init).')
        del checkpoint, state, fixed

    optimizer = optim.AdamW([p for p in model.parameters() if p.requires_grad], lr=cfg.lr, weight_decay=cfg.wdecay, eps=1e-8)
    lr_scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=cfg.lr,
        total_steps=cfg.total_step+100,
        pct_start=0.05,
        cycle_momentum=False,
        anneal_strategy='linear'
    )

    train_loader, model, optimizer, lr_scheduler = accelerator.prepare(train_loader, model, optimizer, lr_scheduler)
    model.to(accelerator.device)

    total_step = 0
    should_keep_training = True
    while should_keep_training:
        active_train_loader = train_loader
        model.train()
        # 冻结BN
        base_model = accelerator.unwrap_model(model)
        if hasattr(base_model, 'freeze_bn'):
            base_model.freeze_bn()

        for data in tqdm(active_train_loader, dynamic_ncols=True, disable=not accelerator.is_main_process):
            _, left, right, disp_gt, occ_mask, valid = [x for x in data]
            amp_ctx = torch.cuda.amp.autocast(dtype=(torch.bfloat16 if getattr(accelerator, 'mixed_precision', 'no') == 'bf16' else torch.float16)) if torch.cuda.is_available() else nullcontext()
            with amp_ctx:
                # 使用unwrap后的底层模型以访问infer_mono等方法
                core_model = accelerator.unwrap_model(model)
                disp_init_pred = compute_hde_outputs_fullres(core_model, left, right)
                # 按train_sceneflow的初始项损失
                mag = torch.sum(disp_gt**2, dim=1).sqrt()
                valid_mask = ((valid >= 0.5) & (mag < cfg.max_disp)).unsqueeze(1)
                assert valid_mask.shape == disp_gt.shape, [valid_mask.shape, disp_gt.shape]
                init_valid = valid_mask.bool() & ~torch.isnan(disp_init_pred)
                loss_init = F.l1_loss(disp_init_pred[init_valid], disp_gt[init_valid], reduction='mean')
                
                
                loss = loss_init

            accelerator.backward(loss)
            accelerator.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            lr_scheduler.step()
            optimizer.zero_grad(set_to_none=True)

            total_step += 1
            loss_red = accelerator.reduce(loss.detach(), reduction='mean')
            if not isinstance(loss_red, torch.Tensor):
                loss_red = torch.tensor(loss_red, dtype=torch.float32, device=loss.device if isinstance(loss, torch.Tensor) else 'cpu')
            loss_scalar = float(loss_red.item())

            if total_step % 100 == 0 and accelerator.is_main_process:
                print(f"\n=== Step {total_step} HDE-only Training Metrics ===")
                print(f"Loss: {loss_scalar:.6f}")
                print(f"L1: {float(loss_init.detach().item()) if isinstance(loss_init, torch.Tensor) else float(loss_init):.6f}")
                print(f"Learning Rate: {optimizer.param_groups[-1]['lr']:.8f}")
                print("=" * 40)

            # 周期性保存checkpoint（默认每1000步）
            save_freq = int(getattr(cfg, 'save_frequency', 1000))
            if (total_step > 0) and (total_step % save_freq == 0):
                if accelerator.is_main_process:
                    save_path = Path(cfg.save_path + f'/{total_step}_hde_only.pth')
                    model_save = accelerator.unwrap_model(model)
                    torch.save(model_save.state_dict(), save_path)
                    del model_save

            del left, right, disp_gt, occ_mask, valid, disp_init_pred, loss, loss_init
            if (total_step % 50 == 0) and torch.cuda.is_available():
                torch.cuda.empty_cache()

            if total_step == cfg.total_step:
                should_keep_training = False
                break

    if accelerator.is_main_process:
        save_path = Path(cfg.save_path + '/final_hde_only.pth')
        model_save = accelerator.unwrap_model(model)
        torch.save(model_save.state_dict(), save_path)
        del model_save

    accelerator.end_training()

if __name__ == '__main__':
    main()
# 选择性权重加载功能

## 概述

训练脚本现在支持选择性权重加载功能，允许您控制从预训练检查点中加载哪些模块的权重。

## 配置参数

在配置文件（如 `config/train_sceneflow.yaml`）中添加以下参数：

```yaml
# 权重加载配置
selective_loading: true  # true: 只加载指定模块; false: 加载所有可用权重
```

## 使用方法

### 1. 选择性加载（推荐）

设置 `selective_loading: true`（默认值）：

```yaml
selective_loading: true
restore_ckpt: 'checkpoints/pretrained_model.pth'
```

**效果**：
- ✅ 加载 `mono_encoder` 的权重（特征提取器）
- ✅ 加载 `feat_decoder` 的权重（特征解码器）
- 🔄 其他模块保持随机初始化（如 `update_block`、`cost_agg` 等）

**适用场景**：
- 使用预训练的特征提取器，但希望立体匹配算法从头训练
- 避免预训练权重对新任务的负面影响
- 微调特定模块

### 2. 完整加载

设置 `selective_loading: false`：

```yaml
selective_loading: false
restore_ckpt: 'checkpoints/pretrained_model.pth'
```

**效果**：
- ✅ 加载所有可用的预训练权重
- 🔄 缺失或形状不匹配的权重保持随机初始化

**适用场景**：
- 继续训练已有模型
- 在相似任务上进行微调
- 完整的模型恢复

## 日志输出

### 选择性加载日志示例

```
============================================================
Loading selective pretrained weights...
Target modules: mono_encoder, feat_decoder
============================================================
  ✅ mono_encoder: loaded 175/175 parameters
  ✅ feat_decoder: loaded 58/58 parameters
============================================================
✅ Total: loaded 233/233 parameters from target modules
🔄 Skipped modules (will use random initialization): update_block, cost_agg, ...
============================================================
Loaded mono_encoder and feat_decoder weights from checkpoints/pretrained_model.pth successfully
```

### 完整加载日志示例

```
Loaded all available weights from checkpoints/pretrained_model.pth successfully
```

## 高级用法

如果需要自定义加载的模块，可以修改 `train_sceneflow.py` 中的函数调用：

```python
# 自定义要加载的模块
target_modules = ['mono_encoder', 'feat_decoder', 'cost_agg']
load_selective_pretrained_weights(model, ckpt, target_modules=target_modules)
```

## 注意事项

1. **默认行为**：如果配置文件中没有 `selective_loading` 参数，默认为 `true`（选择性加载）
2. **兼容性**：该功能与现有的训练流程完全兼容
3. **安全性**：函数会检查参数形状匹配，避免加载不兼容的权重
4. **灵活性**：可以通过修改 `target_modules` 列表来自定义加载的模块

## 模块说明

- **mono_encoder**: 单目深度估计的特征提取器（通常是预训练的 ViT）
- **feat_decoder**: 特征解码器，将编码器特征转换为多尺度特征
- **update_block**: GRU 更新模块，用于迭代优化视差
- **cost_agg**: 代价聚合模块，用于构建代价体积
- **其他模块**: 包括分类器、注意力模块等

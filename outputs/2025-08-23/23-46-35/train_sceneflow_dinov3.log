[2025-08-23 23:46:44,603][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-08-23 23:46:48,815][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-08-23 23:46:48,815][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-08-23 23:46:48,817][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-08-23 23:46:51,589][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-08-23 23:46:54,756][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-08-23 23:46:54,771][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-08-23 23:46:54,772][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-08-23 23:46:56,914][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-08-23 23:46:56,916][root][INFO] - Adding 67158 samples from SceneFlow
[2025-08-23 23:46:57,357][dinov3][INFO] - using base=100 for rope new
[2025-08-23 23:46:57,358][dinov3][INFO] - using min_period=None for rope new
[2025-08-23 23:46:57,358][dinov3][INFO] - using max_period=None for rope new
[2025-08-23 23:46:57,358][dinov3][INFO] - using normalize_coords=separate for rope new
[2025-08-23 23:46:57,359][dinov3][INFO] - using shift_coords=None for rope new
[2025-08-23 23:46:57,359][dinov3][INFO] - using rescale_coords=2 for rope new
[2025-08-23 23:46:57,360][dinov3][INFO] - using jitter_coords=None for rope new
[2025-08-23 23:46:57,360][dinov3][INFO] - using dtype=fp32 for rope new
[2025-08-23 23:46:57,361][dinov3][INFO] - using mlp layer as FFN
[2025-08-23 23:46:58,996][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-08-23 23:46:58,997][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-08-23 23:46:58,998][root][INFO] - Adding 67158 samples from SceneFlow
[2025-08-23 23:46:58,998][root][INFO] - Adding 67158 samples from SceneFlow
[2025-08-23 23:46:59,007][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-08-23 23:46:59,009][root][INFO] - Adding 67158 samples from SceneFlow
[2025-08-23 23:46:59,360][dinov3][INFO] - using base=100 for rope new
[2025-08-23 23:46:59,360][dinov3][INFO] - using min_period=None for rope new
[2025-08-23 23:46:59,361][dinov3][INFO] - using max_period=None for rope new
[2025-08-23 23:46:59,362][dinov3][INFO] - using normalize_coords=separate for rope new
[2025-08-23 23:46:59,362][dinov3][INFO] - using shift_coords=None for rope new
[2025-08-23 23:46:59,363][dinov3][INFO] - using rescale_coords=2 for rope new
[2025-08-23 23:46:59,363][dinov3][INFO] - using jitter_coords=None for rope new
[2025-08-23 23:46:59,364][dinov3][INFO] - using dtype=fp32 for rope new
[2025-08-23 23:46:59,365][dinov3][INFO] - using mlp layer as FFN
[2025-08-23 23:46:59,392][dinov3][INFO] - using base=100 for rope new
[2025-08-23 23:46:59,393][dinov3][INFO] - using min_period=None for rope new
[2025-08-23 23:46:59,393][dinov3][INFO] - using max_period=None for rope new
[2025-08-23 23:46:59,394][dinov3][INFO] - using normalize_coords=separate for rope new
[2025-08-23 23:46:59,394][dinov3][INFO] - using shift_coords=None for rope new
[2025-08-23 23:46:59,395][dinov3][INFO] - using rescale_coords=2 for rope new
[2025-08-23 23:46:59,395][dinov3][INFO] - using jitter_coords=None for rope new
[2025-08-23 23:46:59,396][dinov3][INFO] - using dtype=fp32 for rope new
[2025-08-23 23:46:59,396][dinov3][INFO] - using mlp layer as FFN
[2025-08-23 23:46:59,398][dinov3][INFO] - using base=100 for rope new
[2025-08-23 23:46:59,398][dinov3][INFO] - using min_period=None for rope new
[2025-08-23 23:46:59,399][dinov3][INFO] - using max_period=None for rope new
[2025-08-23 23:46:59,399][dinov3][INFO] - using normalize_coords=separate for rope new
[2025-08-23 23:46:59,399][dinov3][INFO] - using shift_coords=None for rope new
[2025-08-23 23:46:59,400][dinov3][INFO] - using rescale_coords=2 for rope new
[2025-08-23 23:46:59,400][dinov3][INFO] - using jitter_coords=None for rope new
[2025-08-23 23:46:59,400][dinov3][INFO] - using dtype=fp32 for rope new
[2025-08-23 23:46:59,401][dinov3][INFO] - using mlp layer as FFN

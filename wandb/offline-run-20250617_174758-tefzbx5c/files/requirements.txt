local-attention==1.11.1
pip==25.0.1
nvidia-cublas-cu11==*********
websockets==15.0
pooch==1.8.2
Werkzeug==3.1.3
numpy==2.0.1
numpy==1.26.4
msgpack==1.1.0
timm==1.0.15
termcolor==2.5.0
tzdata==2025.1
av==14.2.0
docker-pycreds==0.4.0
safetensors==0.5.3
axial_positional_embedding==0.3.12
torchvision==0.21.0+cu118
sentencepiece==0.2.0
jmespath==0.10.0
peft==0.12.0
safehttpx==0.1.6
lazy_loader==0.4
frozenlist==1.5.0
opt_einsum==3.4.0
fonttools==4.56.0
xxhash==3.5.0
datasets==3.2.0
setproctitle==1.3.5
cached-property==1.5.2
reformer-pytorch==1.4.4
packaging==24.2
aiofiles==23.2.1
orjson==3.10.15
et_xmlfile==2.0.0
scikit-learn==1.6.1
GitPython==3.1.44
OpenEXR==3.3.2
tqdm==4.65.2
tensorboard-data-server==0.7.2
mpmath==1.3.0
yarl==1.18.3
accelerate==1.2.1
hyper-connections==0.1.15
pycparser==2.22
typer==0.15.2
scikit-image==0.25.0
ruff==0.9.9
cycler==0.12.1
gradio_client==1.7.2
typing_extensions==4.12.2
PyWavelets==1.8.0
audioread==3.0.1
fire==0.7.0
python-multipart==0.0.20
huggingface-hub==0.29.1
kiwisolver==1.4.8
nltk==3.9.1
httpx==0.28.1
openxlab==0.1.2
sympy==1.13.1
tomlkit==0.13.2
propcache==0.3.0
nvidia-curand-cu11==*********
idna==3.10
requests==2.28.2
gradio==5.18.0
trl==0.9.6
antlr4-python3-runtime==4.9.3
h5py==3.13.0
networkx==3.4.2
networkx==3.3
PyYAML==6.0.2
hydra-core==1.3.2
python-dateutil==2.9.0.post0
anyio==4.8.0
aliyun-python-sdk-core==2.16.0
markdown-it-py==3.0.0
tyro==0.8.14
seaborn==0.13.2
torch==2.6.0+cu118
semantic-version==2.10.0
soundfile==0.13.1
matplotlib==3.10.1
mdurl==0.1.2
dill==0.3.8
opencv-python==*********
aiosignal==1.3.2
nvidia-nvtx-cu11==11.8.86
filelock==3.14.0
product_key_memory==0.2.11
pyarrow==19.0.1
tokenizers==0.21.0
charset-normalizer==3.4.1
httpcore==1.0.7
click==8.1.8
wheel==0.45.1
wandb==0.19.9
nvidia-cufft-cu11==*********
pandas==2.2.3
urllib3==1.26.20
protobuf==5.29.4
platformdirs==4.3.6
cffi==1.17.1
numba==0.61.0
pycryptodome==3.22.0
absl-py==2.3.0
nvidia-cusolver-cu11==*********
attrs==25.1.0
pydantic_core==2.27.2
fsspec==2024.6.1
regex==2024.11.6
shellingham==1.5.4
MarkupSafe==2.1.5
imageio==2.37.0
contourpy==1.3.1
pillow==11.1.0
pillow==11.0.0
gitdb==4.0.12
multiprocess==0.70.16
xformers==0.0.29.post3
six==1.17.0
omegaconf==2.3.0
Jinja2==3.1.4
triton==3.2.0
nvidia-cusparse-cu11==*********
rich==13.4.2
CoLT5-attention==0.11.1
nvidia-cuda-nvrtc-cu11==11.8.89
starlette==0.46.0
sniffio==1.3.1
certifi==2025.1.31
pytorch-triton==0.0.1
aiohttp==3.11.13
librosa==0.10.2.post1
setuptools==78.1.0
h11==0.14.0
jieba==0.42.1
tifffile==2025.2.18
llamafactory==0.9.2.dev0
nvidia-cuda-cupti-cu11==11.8.87
Pygments==2.19.1
oss2==2.17.0
scipy==1.15.1
scipy==1.15.2
rouge-chinese==1.0.3
pytz==2023.4
aliyun-python-sdk-kms==2.16.5
einops==0.8.1
tensorboard==2.19.0
annotated-types==0.7.0
shtab==1.7.1
Markdown==3.8
grpcio==1.71.0
tiktoken==0.9.0
nvidia-cudnn-cu11==********
nvidia-nccl-cu11==2.21.5
llvmlite==0.44.0
cryptography==44.0.2
smmap==5.0.2
fastapi==0.115.11
sse-starlette==2.2.1
sentry-sdk==2.26.0
ffmpy==0.5.0
openpyxl==3.1.5
pydub==0.25.1
crcmod==1.7
aiohappyeyeballs==2.4.8
psutil==7.0.0
pyparsing==3.2.1
pydantic==2.10.6
torchaudio==2.6.0+cu118
docstring_parser==0.16
joblib==1.4.2
transformers==4.49.0
multidict==6.1.0
threadpoolctl==3.5.0
decorator==5.2.1
uvicorn==0.34.0
soxr==0.5.0.post1
nvidia-cuda-runtime-cu11==11.8.89
llamafactory==0.9.2.dev0
jaraco.collections==5.1.0
jaraco.context==5.3.0
platformdirs==4.2.2
more-itertools==10.3.0
packaging==24.2
jaraco.text==3.12.1
inflect==7.3.1
typing_extensions==4.12.2
jaraco.functools==4.0.1
autocommand==2.2.2
zipp==3.19.2
tomli==2.0.1
wheel==0.45.1
backports.tarfile==1.2.0
typeguard==4.3.0
importlib_metadata==8.0.0
